
import { ComboBox, IComboBoxOption, Icon, mergeStyleSets, MessageBar, MessageBarType, Spinner, SpinnerSize, TextField } from "@fluentui/react";
import React from "react";
import { L } from "../../../lib/abpUtility";
import AppConfig from "../../../lib/appconfig";
import AppConsts from "../../../lib/appconst";
import vehicleCodeService from "../../../services/vehicleCode/vehicleCodeService";
import { myTheme } from "../../../styles/theme";
import { catchErrorMessage } from "../../../utils/utils";
import { ICustomInputsBoxGeneric, IEuroTaxEngineCapacity, IEuroTaxEnginePower, IEuroTaxFuelType, IEurotaxVehicleBrands, IEurotaxVehicleConfiguration, IEurotaxVehicleTypes } from "./types";

var _ = require('lodash');

const classNames = mergeStyleSets({
    hide: {
        display: 'none !important',
    },
    fontBold: {
        fontWeight: '800',
    },
    toolbar: {
        selectors: {
            '& .ms-Pivot': {
                display: 'none',
            }
        }
    },
    messageBar: {
        width: 'fit-content'
    },
    messageBarMargin: {
        marginTop: '15px'
    },
    closeIcon: {
        position: 'absolute',
        top: 20,
        right: 20,
        cursor: 'pointer',
        transition: 'all 150ms',
        selectors: {
            ':hover': {
                transform: 'scale(1.1)',
            },
            ':active': {
                transform: 'scale(0.9) rotate(90deg)',
            },
        }
    },
    customInputsWrapper: {
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        width: 'fit-content',
        height: 'auto',
        marginTop: '20px',
        padding: '20px',
        border: `1px solid ${myTheme.palette.themePrimary}`,
        borderRadius: '3px',
        minWidth: '502px',
        boxSizing: 'border-box',
    },
    comboBoxStyles: {
        width: '100%',
        marginTop: '15px',
    },
    loadSpinner: {
        display: 'inline-flex',
        marginTop: '15px',
        marginLeft: 'auto',
        marginRight: 'auto',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    loadSpinnerTopBar: {
        display: 'inline-flex',
        marginLeft: '15px',
        marginRight: 'auto',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
});

export interface IEurotaxExpertInfoMixedCustomInputsBoxProps {
    showCustomInputsBox: boolean,
    customInputsData: any,
    onInputChange: (id: string, value: any) => void,
    onToggleClose: () => void,
    mapKeyToId: (mapType: string, key: string) => string,
    setEurotaxInfoexpertFormData?: (id: string, value: string) => void;
    toggleAsyncActionFlag: (newState: boolean, forceUpdate: boolean) => void;
}

type IEurotaxExpertInfoMixedCustomInputsBoxState = ICustomInputsBoxGeneric & {
    vehicleTypes: IEurotaxVehicleTypes;
    vehicleConfiguration: IEurotaxVehicleConfiguration;
    vehicleBrands: IEurotaxVehicleBrands;
    enginePower: IEuroTaxEngineCapacity;
    engineCapacity: IEuroTaxEnginePower;
    fuelType: IEuroTaxFuelType;
    customInputsPrevData: {
        vehicleTypeId: string,
        vehicleBrandId: string,
        productionYear: number,
        engineCapacity: string,
        enginePower: number,
        fuelType: string,
        vehicleModelId: string,
        vehicleConfigurationEurotaxId: string,
        vehicleConfigurationInfoExpertId: string,
    },
    maxErrorsCount: number,
    operationEurotaxSuccess: boolean,
    operationInfoEkspertSuccess: boolean,
    valueBeforeDebounce: any,
    bothSet: boolean;
    infoexpertOptions: IComboBoxOption[];
};

export class EurotaxExpertInfoMixedCustomInputsBox extends React.Component<IEurotaxExpertInfoMixedCustomInputsBoxProps, IEurotaxExpertInfoMixedCustomInputsBoxState> {
    constructor(props: IEurotaxExpertInfoMixedCustomInputsBoxProps) {
        super(props);
    
        this.state = {
            ...this.state,
            date: new Date(),
            customInputsAsyncActionInProgress: false,
            vehicleTypes: { errorsCount: 0, error: "", gettingDataInProgress: false, types: [] },
            vehicleBrands: { errorsCount: 0, error: "", gettingDataInProgress: false, /*, typeId: [...brands] */ },
            vehicleModels: { errorsCount: 0, error: "", gettingDataInProgress: false, /*, brandId: [...models]" */ },
            vehicleConfiguration: { errorsCount: 0, error: "", gettingDataInProgress: false, /*, modelId: [...configurations]" */ },
            engineCapacity: { errorsCount: 0, error: "", gettingDataInProgress: false, /*, modelId: [...configurations]" */ },
            enginePower: { errorsCount: 0, error: "", gettingDataInProgress: false, /*, modelId: [...configurations]" */ },
            fuelType: { errorsCount: 0, error: "", gettingDataInProgress: false, /*, modelId: [...configurations]" */ },
            customInputsPrevData: {
                vehicleTypeId: '',
                vehicleBrandId: '',
                productionYear: 0,
                engineCapacity: '',
                enginePower: 0,
                fuelType: '',
                vehicleModelId: '',
                vehicleConfigurationEurotaxId: '',
                vehicleConfigurationInfoExpertId: '',
            },
            maxErrorsCount: AppConfig.eurotaxCollectDataMaxConnectionErrorsCount,
            operationEurotaxSuccess: false,
            operationInfoEkspertSuccess: false,
            valueBeforeDebounce: {},
            bothSet: false,
        };
    }

    private debouncedOnInputChange: any = _.debounce((inputKey: string, value: any) => {
        if(inputKey === 'productionYear') {
            this.props.onInputChange(this.props.mapKeyToId("mapAttributeNameToId", inputKey), value.toString());
        }
        this.props.onInputChange(inputKey, value);
    }, AppConsts.defaultInputsDelay, []);

    private setEurotaxInfoexpertFormData(id: string, value: string) {
        if(this.props.setEurotaxInfoexpertFormData) {
            this.props.setEurotaxInfoexpertFormData(id, value);
        }
    }

    private async getDataForCustomInputs(requestType: string, vehicleTypeId?: string, vehicleBrandId?: string, productionYear?: string, fuelTypeInput?: string, engineCapacityInput?: string, vehicleModelId?: string, enginePowerInput?: number, vehicleConfigurationEurotaxId?: string) {
        const { vehicleTypes, vehicleBrands, vehicleModels, vehicleConfiguration, engineCapacity, enginePower, fuelType, customInputsPrevData } = this.state;

        switch(requestType) {
            case "getTypes":
                if(!vehicleTypes['gettingDataInProgress']) {
                    vehicleTypes['gettingDataInProgress'] = true;

                    await vehicleCodeService.getVehicleTypes().then((response: any) => {
                        if(response && response.items) {
                            vehicleTypes["error"] = "";
                            vehicleTypes["types"] = response.items;
                        }
                
                        vehicleTypes['errorsCount'] = 0;
                        vehicleTypes['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, vehicleTypes: vehicleTypes, customInputsAsyncActionInProgress: false, bothSet: false, 
                            customInputsPrevData: {...customInputsPrevData, 
                                vehicleConfigurationEurotaxId: '',
                                vehicleConfigurationInfoExpertId: '',
                                vehicleBrandId: '',
                                productionYear: 0,
                                engineCapacity: '',
                                enginePower: 0,
                                fuelType: '',
                                vehicleModelId: '',
                            }}
                        ));
                    }).catch((error: any) => {
                        vehicleTypes["errorsCount"]++;
                        vehicleTypes["error"] = catchErrorMessage(error);
                        vehicleTypes['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, vehicleTypes: vehicleTypes, customInputsAsyncActionInProgress: false, bothSet: false,
                            customInputsPrevData: {...customInputsPrevData,
                                vehicleConfigurationEurotaxId: '',
                                vehicleConfigurationInfoExpertId: '',
                                vehicleBrandId: '',
                                productionYear: 0,
                                engineCapacity: '',
                                enginePower: 0,
                                fuelType: '',
                                vehicleModelId: '',
                            }}
                        ));
                    });
                }
            break;
            
            case "getBrands":
                if(!vehicleBrands['gettingDataInProgress']) {
                    vehicleBrands['gettingDataInProgress'] = true;

                    this.setEurotaxInfoexpertFormData('type', vehicleTypeId!);

                    await vehicleCodeService.getVehicleBrands(vehicleTypeId!).then((response: any) => {
                        if(response && response.items) {
                            vehicleBrands["error"] = "";
                            vehicleBrands[vehicleTypeId!] = response.items;
                        }
                        
                        vehicleBrands['errorsCount'] = 0;
                        vehicleBrands['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, vehicleBrands: vehicleBrands, customInputsAsyncActionInProgress: false, bothSet: false,
                            customInputsPrevData: {...customInputsPrevData, 
                                vehicleTypeId: vehicleTypeId!,
                                vehicleConfigurationEurotaxId: '',
                                vehicleConfigurationInfoExpertId: '',
                                productionYear: 0,
                                engineCapacity: '',
                                enginePower: 0,
                                fuelType: '',
                                vehicleModelId: '',
                            }}
                        ));
                    }).catch((error: any) => {
                        vehicleBrands["errorsCount"]++;
                        vehicleBrands["error"] = catchErrorMessage(error);
                        vehicleBrands['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, vehicleBrands: vehicleBrands, customInputsAsyncActionInProgress: false, bothSet: false,
                            customInputsPrevData: {...customInputsPrevData,
                                vehicleTypeId: vehicleTypeId!,
                                vehicleConfigurationEurotaxId: '',
                                vehicleConfigurationInfoExpertId: '',
                                productionYear: 0,
                                engineCapacity: '',
                                enginePower: 0,
                                fuelType: '',
                                vehicleModelId: '',
                            }}
                        ));
                    });
                }
            break;

            case "getFuelType":
                if(!fuelType['gettingDataInProgress']) {
                    fuelType['gettingDataInProgress'] = true;
                    
                    this.setEurotaxInfoexpertFormData('brand', vehicleBrandId!);

                    await vehicleCodeService.getVehicleFuelTypes(vehicleTypeId!, vehicleBrandId!, productionYear!).then((response: any) => {
                        if(response && response.items) {
                            fuelType["error"] = "";
                            fuelType[vehicleTypeId!] = {};
                            fuelType[vehicleTypeId!][vehicleBrandId!] = {};
                            fuelType[vehicleTypeId!][vehicleBrandId!][productionYear!] = response.items;
                        }
                
                        fuelType['errorsCount'] = 0;
                        fuelType['gettingDataInProgress'] = false;

                        this.setState((prevState) => ({...prevState, fuelType: fuelType, customInputsAsyncActionInProgress: false, bothSet: false,
                            customInputsPrevData: {...customInputsPrevData,
                                vehicleBrandId: vehicleBrandId!, 
                                productionYear: parseInt(productionYear!), 
                                vehicleConfigurationEurotaxId: '',
                                vehicleConfigurationInfoExpertId: '',
                                engineCapacity: '',
                                enginePower: 0,
                                vehicleModelId: '',
                            }    
                        }));
                    }).catch((error: any) => {
                        fuelType["errorsCount"]++;
                        fuelType["error"] = catchErrorMessage(error);
                        fuelType['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, fuelType: fuelType, customInputsAsyncActionInProgress: false, bothSet: false,
                            customInputsPrevData: {...customInputsPrevData,
                                vehicleBrandId: vehicleBrandId!, 
                                productionYear: parseInt(productionYear!), 
                                vehicleConfigurationEurotaxId: '',
                                vehicleConfigurationInfoExpertId: '',
                                engineCapacity: '',
                                enginePower: 0,
                                vehicleModelId: '',
                            }    
                        }));
                    });
                }
            break;

            case "getEngineCapacity":
                if(!engineCapacity['gettingDataInProgress']) {
                    engineCapacity['gettingDataInProgress'] = true;

                    this.setEurotaxInfoexpertFormData('year', productionYear!);
                    this.setEurotaxInfoexpertFormData('fuelType', fuelTypeInput!);

                    await vehicleCodeService.getVehicleEngineCapacities(vehicleTypeId!, vehicleBrandId!, productionYear!, fuelTypeInput!).then((response: any) => {
                        if(response && response.items) {
                            engineCapacity["error"] = "";
                            engineCapacity[vehicleTypeId!] = {};
                            engineCapacity[vehicleTypeId!][vehicleBrandId!] = {};
                            engineCapacity[vehicleTypeId!][vehicleBrandId!][productionYear!] = {};
                            engineCapacity[vehicleTypeId!][vehicleBrandId!][productionYear!][fuelTypeInput!] = response.items;
                        }
                        engineCapacity['errorsCount'] = 0;
                        engineCapacity['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, engineCapacity: engineCapacity, customInputsAsyncActionInProgress: false, bothSet: false,
                            customInputsPrevData: {...customInputsPrevData,
                                fuelType: fuelTypeInput!,
                                vehicleConfigurationEurotaxId: '',
                                vehicleConfigurationInfoExpertId: '',
                                enginePower: 0,
                                vehicleModelId: '',
                            } 
                        }));
                    }).catch((error: any) => {
                        engineCapacity["errorsCount"]++;
                        engineCapacity["error"] = catchErrorMessage(error);
                        engineCapacity['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, engineCapacity: engineCapacity, customInputsAsyncActionInProgress: false, bothSet: false,
                            customInputsPrevData: {...customInputsPrevData,
                                fuelType: fuelTypeInput!,
                                vehicleConfigurationEurotaxId: '',
                                vehicleConfigurationInfoExpertId: '',
                                enginePower: 0,
                                vehicleModelId: '',
                            } 
                        }));
                    });
                }
            break;

            case "getModels":
                if(!vehicleModels['gettingDataInProgress']) {
                    vehicleModels['gettingDataInProgress'] = true;

                    this.setEurotaxInfoexpertFormData('engineCapacity', engineCapacityInput!);

                    await vehicleCodeService.getVehicleModels(vehicleTypeId!, vehicleBrandId!, productionYear!, fuelTypeInput!, engineCapacityInput!).then((response: any) => {
                        if(response && response.items) {
                            vehicleModels["error"] = "";
                            vehicleModels[vehicleTypeId!] = {};
                            vehicleModels[vehicleTypeId!][vehicleBrandId!] = {};
                            vehicleModels[vehicleTypeId!][vehicleBrandId!][productionYear!] = {};
                            vehicleModels[vehicleTypeId!][vehicleBrandId!][productionYear!][fuelTypeInput!] = {};
                            vehicleModels[vehicleTypeId!][vehicleBrandId!][productionYear!][fuelTypeInput!][engineCapacityInput!] = response.items;
                        }
                
                        vehicleModels['errorsCount'] = 0;
                        vehicleModels['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, vehicleModels: vehicleModels, customInputsAsyncActionInProgress: false, bothSet: false,
                            customInputsPrevData: {...customInputsPrevData,
                                engineCapacity: engineCapacityInput!,
                                vehicleConfigurationEurotaxId: '',
                                vehicleConfigurationInfoExpertId: '',
                                enginePower: 0,
                            }    
                        }));
                    }).catch((error: any) => {
                        vehicleModels["errorsCount"]++;
                        vehicleModels["error"] = catchErrorMessage(error);
                        vehicleModels['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, vehicleModels: vehicleModels, customInputsAsyncActionInProgress: false, bothSet: false,
                            customInputsPrevData: {...customInputsPrevData,
                                engineCapacity: engineCapacityInput!,
                                vehicleConfigurationEurotaxId: '',
                                vehicleConfigurationInfoExpertId: '',
                                enginePower: 0,
                            }    
                        }));
                    });
                }
            break;

            case "getEnginePower":
                if(!enginePower['gettingDataInProgress']) {
                    enginePower['gettingDataInProgress'] = true;

                    this.setEurotaxInfoexpertFormData('vehicleModelId', vehicleModelId!);

                    await vehicleCodeService.getVehicleEnginePowers(vehicleTypeId!, vehicleBrandId!, productionYear!, fuelTypeInput!, engineCapacityInput!, vehicleModelId!).then((response: any) => {
                        if(response && response.items) {
                            enginePower["error"] = "";
                            enginePower[vehicleTypeId!] = {};
                            enginePower[vehicleTypeId!][vehicleBrandId!] = {};
                            enginePower[vehicleTypeId!][vehicleBrandId!][productionYear!] = {};
                            enginePower[vehicleTypeId!][vehicleBrandId!][productionYear!][fuelTypeInput!] = {};
                            enginePower[vehicleTypeId!][vehicleBrandId!][productionYear!][fuelTypeInput!][engineCapacityInput!] = {};
                            enginePower[vehicleTypeId!][vehicleBrandId!][productionYear!][fuelTypeInput!][engineCapacityInput!][vehicleModelId!] = response.items;
                        }
                
                        enginePower['errorsCount'] = 0;
                        enginePower['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, enginePower: enginePower, customInputsAsyncActionInProgress: false, bothSet: false,
                            customInputsPrevData: {...customInputsPrevData,
                                vehicleModelId: vehicleModelId!,
                                vehicleConfigurationEurotaxId: '',
                                vehicleConfigurationInfoExpertId: '',
                            } 
                        }));
                    }).catch((error: any) => {
                        enginePower["errorsCount"]++;
                        enginePower["error"] = catchErrorMessage(error);
                        enginePower['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, enginePower: enginePower, customInputsAsyncActionInProgress: false, bothSet: false,
                            customInputsPrevData: {...customInputsPrevData,
                                vehicleModelId: vehicleModelId!,
                                vehicleConfigurationEurotaxId: '',
                                vehicleConfigurationInfoExpertId: '',
                            } 
                        }));
                    });
                }
            break;

            case "getConfiguration":
                if(!vehicleConfiguration['gettingDataInProgress']) {
                    vehicleConfiguration['gettingDataInProgress'] = true;

                    this.setEurotaxInfoexpertFormData('enginePower', enginePowerInput!.toString());

                    await vehicleCodeService.getVehicleCodes(vehicleTypeId!, vehicleBrandId!, productionYear!, fuelTypeInput!, engineCapacityInput!, vehicleModelId!, enginePowerInput!).then(async (response: any) => {
                        if(response) {
                            vehicleConfiguration["error"] = "";
                            vehicleConfiguration[vehicleTypeId!] = {};
                            vehicleConfiguration[vehicleTypeId!][vehicleBrandId!] = {};
                            vehicleConfiguration[vehicleTypeId!][vehicleBrandId!][productionYear!] = {};
                            vehicleConfiguration[vehicleTypeId!][vehicleBrandId!][productionYear!][fuelTypeInput!] = {};
                            vehicleConfiguration[vehicleTypeId!][vehicleBrandId!][productionYear!][fuelTypeInput!][engineCapacityInput!] = {};
                            vehicleConfiguration[vehicleTypeId!][vehicleBrandId!][productionYear!][fuelTypeInput!][engineCapacityInput!][vehicleModelId!] = {};
                            vehicleConfiguration[vehicleTypeId!][vehicleBrandId!][productionYear!][fuelTypeInput!][engineCapacityInput!][vehicleModelId!][enginePowerInput!] = response;
                        }
                        
                        vehicleConfiguration['errorsCount'] = 0;
                        this.setState((prevState) => ({...prevState, vehicleConfiguration: vehicleConfiguration, customInputsAsyncActionInProgress: false, bothSet: false,
                            customInputsPrevData: {...customInputsPrevData,
                                enginePowerInput: enginePowerInput!,
                            }
                        }));
                        vehicleConfiguration['gettingDataInProgress'] = false;
                    }).catch((error: any) => {
                        vehicleConfiguration["errorsCount"]++;
                        vehicleConfiguration["error"] = catchErrorMessage(error);
                        vehicleConfiguration['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, vehicleConfiguration: vehicleConfiguration, customInputsAsyncActionInProgress: false, bothSet: false,
                            customInputsPrevData: {...customInputsPrevData,
                                enginePowerInput: enginePowerInput!,
                            }
                        }));
                    });
                }
            break;

            case "getInfoEkspertCodes":
                if(!vehicleConfiguration['gettingDataInProgress']) {
                    vehicleConfiguration['gettingDataInProgress'] = true;
                    this.props.toggleAsyncActionFlag(true, false);

                    this.setEurotaxInfoexpertFormData('eurotaxCarId', vehicleConfigurationEurotaxId!);
                    
                    await vehicleCodeService.getInfoEkspertCode(parseInt(vehicleConfigurationEurotaxId!), parseInt(productionYear!)).then(async (response: any) => {
                        if(response) {
                            vehicleConfiguration["error"] = "";
                            vehicleConfiguration[vehicleTypeId!][vehicleBrandId!][productionYear!][fuelTypeInput!][engineCapacityInput!][vehicleModelId!][enginePowerInput!] = {
                                ...vehicleConfiguration[vehicleTypeId!][vehicleBrandId!][productionYear!][fuelTypeInput!][engineCapacityInput!][vehicleModelId!][enginePowerInput!],
                                'infoExperts': [response]
                            };

                            if(response.dmc) {
                                this.setEurotaxInfoexpertFormData('dmc', response.dmc);
                            }

                            if(response.netWeight && response.netWeight > 0) {
                                this.setEurotaxInfoexpertFormData('netWeight', response.netWeight);
                            }

                            if(response.capacity && response.capacity > 0) {
                                this.setEurotaxInfoexpertFormData('capacity', response.capacity);
                            }
                            
                            if(response.id) {
                                this.setEurotaxInfoexpertFormData('infoExpertId', response.id);
                                this.setState({infoexpertOptions:[{key: response.id.toString(), text: response.name}]});
                            }

                            if(response.name) {
                                this.setEurotaxInfoexpertFormData('infoExpertName', response.name);
                                this.props.onInputChange('vehicleConfigurationInfoExpertName', response.name);
                            }
                        }

                        vehicleConfiguration['errorsCount'] = 0;
                        this.setState((prevState) => ({...prevState, vehicleConfiguration: vehicleConfiguration, customInputsAsyncActionInProgress: false, bothSet: false}));
                        vehicleConfiguration['gettingDataInProgress'] = false;
                    }).catch((error: any) => {
                        vehicleConfiguration["errorsCount"]++;
                        vehicleConfiguration["error"] = catchErrorMessage(error);
                        vehicleConfiguration['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, vehicleConfiguration: vehicleConfiguration, customInputsAsyncActionInProgress: false, bothSet: false}));
                    }).finally(() => this.props.toggleAsyncActionFlag(false, false));
                }
            break;
        }
        
        if((!this.state.valueBeforeDebounce || !this.state.valueBeforeDebounce.productionYear) && !!this.props.customInputsData.productionYear) {
            this.setState((prevState) => ({...prevState, valueBeforeDebounce: {...this.state.valueBeforeDebounce, 'productionYear': this.props.customInputsData.productionYear } }));
        }
    }

    private toggleCustomInputsAsyncActionInProgressFlag(newState: boolean) {
        if(this.state.customInputsAsyncActionInProgress !== newState) {
            this.setState((prevState) => ({...prevState, customInputsAsyncActionInProgress: newState }));
        }
    }

    private checkInputsAndResetIfUndefined(inputs: any[]) {
        let copyCustomInputsPrevData: any = {...this.state.customInputsPrevData};

        inputs.forEach((input: any) => {
            if(typeof input.value === 'undefined') {
                copyCustomInputsPrevData[input.key] = input.type === 'number' ? 0 : '';
            }
        });

        if(JSON.stringify(this.state.customInputsPrevData) !== JSON.stringify(copyCustomInputsPrevData)) {
            this.setState((prevState) => ({...prevState, customInputsPrevData: copyCustomInputsPrevData }));
        }
    }

    render() {
        const { operationEurotaxSuccess, operationInfoEkspertSuccess, maxErrorsCount, customInputsAsyncActionInProgress, vehicleTypes, vehicleBrands, fuelType, 
                vehicleModels, vehicleConfiguration, customInputsPrevData, engineCapacity, enginePower, bothSet } = this.state;

        let typeOptions: IComboBoxOption[] = [];
        let brandOptions: IComboBoxOption[] = [];
        let modelOptions: IComboBoxOption[] = [];
        let engineCapacityOptions: IComboBoxOption[] = [];
        let fuelTypeOptions: IComboBoxOption[] = [];
        let enginePowerOptions: IComboBoxOption[] = [];
        let vehicleBrandSelected: boolean = false;
        let configurationOptions: any = {
            eurotax: [] as IComboBoxOption[],
            infoekspert: [] as IComboBoxOption[],
        };
        let errors: any = {
            typeError: '',
            brandError: '',
            fuelTypeError: '',
            yearError: '',
            modelError: '',
            engineCapacityError: '',
            enginePowerError: '',
            configurationError: '',
            eurotaxInfoekspertError: '',
            eurotaxError: '',
            infoekspertError: '',
        };

        const vehicleTypeId = this.props.customInputsData.vehicleTypeId;
        const vehicleBrandId = this.props.customInputsData.vehicleBrandId;
        const productionYear = this.props.customInputsData.productionYear;
        const fuelTypeInput = this.props.customInputsData.fuelType;
        const vehicleModelId = this.props.customInputsData.vehicleModelId;
        const engineCapacityInput = this.props.customInputsData.engineCapacity;
        const enginePowerInput = this.props.customInputsData.enginePower;
        const vehicleConfigurationEurotaxId = this.props.customInputsData.vehicleConfigurationEurotaxId;
        const vehicleConfigurationInfoExpertId = this.props.customInputsData.vehicleConfigurationInfoExpertId;

        this.checkInputsAndResetIfUndefined([
            {key: "vehicleTypeId", value: vehicleTypeId, type: 'string'},
            {key: "vehicleBrandId", value: vehicleBrandId, type: 'string'},
            {key: "productionYear", value: productionYear, type: 'number'},
            {key: "fuelType", value: fuelTypeInput, type: 'string'},
            {key: "vehicleModelId", value: vehicleModelId, type: 'string'},
            {key: "engineCapacity", value: engineCapacityInput, type: 'string'},
            {key: "enginePower", value: enginePowerInput, type: 'number'},
            {key: "vehicleConfigurationEurotaxId", value: vehicleConfigurationEurotaxId, type: 'string'},
            {key: "vehicleConfigurationInfoExpertId", value: vehicleConfigurationInfoExpertId, type: 'string'},
        ]);

        if(vehicleTypes["types"]!.length > 0 && vehicleTypes['error'].length === 0 && vehicleTypes['errorsCount'] < maxErrorsCount) {
            vehicleTypes["types"]!.forEach((type: any) => {
                typeOptions.push({ key: type.key, text: type.translatedName, selected: (vehicleTypeId === type.key ? true : false) });
            });
        } else {
            if(vehicleTypes['errorsCount'] < maxErrorsCount && vehicleTypes['error'].length === 0) {
                this.toggleCustomInputsAsyncActionInProgressFlag(true);
                this.getDataForCustomInputs("getTypes");
            } else {
                errors.typeError = L(vehicleTypes['error']);
            }
        }

        let stopChecking: boolean = false;

        // vehicle types
        if(vehicleTypeId && vehicleTypeId.length > 0) {
            if(vehicleBrands[vehicleTypeId] && vehicleBrands[vehicleTypeId].length > 0 && vehicleBrands['error'].length === 0 && vehicleBrands['errorsCount'] < maxErrorsCount) {
                vehicleBrands[vehicleTypeId].forEach((brand: any, index: number) => {
                    brandOptions.push({ key: brand.key, text: brand.translatedName });
                });
            } else {
                if((vehicleTypeId && vehicleTypeId.length > 0 && customInputsPrevData['vehicleTypeId'] !== vehicleTypeId) && vehicleBrands['errorsCount'] < maxErrorsCount && vehicleTypes['error'].length === 0) {
                    this.toggleCustomInputsAsyncActionInProgressFlag(true);
                    this.getDataForCustomInputs("getBrands", vehicleTypeId);
                } else {
                    errors.brandError = L(vehicleBrands['error']);
                }
            }
        } else {
            errors.brandError = L('Please select vehicle type.');
            stopChecking = true;
        }

        // vehicle brands
        if(vehicleBrandId && vehicleBrandId.length > 0) {
            vehicleBrandSelected = true;
        } else if(!stopChecking) {
            errors.yearError = L('Please select vehicle brand.');
            stopChecking = true;
        }
        
        // production year & fuel type
        if(productionYear) {
            if(fuelType[vehicleTypeId] && fuelType[vehicleTypeId][vehicleBrandId] && fuelType[vehicleTypeId][vehicleBrandId][productionYear] && fuelType[vehicleTypeId][vehicleBrandId][productionYear].length > 0 && fuelType['error'].length === 0 && fuelType['errorsCount'] < maxErrorsCount) {
                fuelType[vehicleTypeId][vehicleBrandId][productionYear].forEach((fuelType: any, index: number) => {
                    fuelTypeOptions.push({ key: fuelType.key, text: fuelType.translatedName });
                });
            } else {
                let hasYear: boolean = false;
                if(fuelType && fuelType[vehicleTypeId] && fuelType[vehicleTypeId][vehicleBrandId] && fuelType[vehicleTypeId][vehicleBrandId].hasOwnProperty(productionYear)) {
                    hasYear = true;
                }

                if((productionYear && productionYear > 0 && (customInputsPrevData['productionYear'] !== productionYear || hasYear === false)) && fuelType['errorsCount'] < maxErrorsCount && fuelType['error'].length === 0) {
                    if(!(productionYear > 1922 && productionYear <= new Date().getFullYear())) {
                        errors.yearError = L("Vehicle production year must be between 1922 and current year.")
                    } else {
                        this.toggleCustomInputsAsyncActionInProgressFlag(true);
                        this.getDataForCustomInputs("getFuelType", vehicleTypeId, vehicleBrandId, productionYear);
                    }
                } else if(productionYear === 0 || errors.yearError.length > 0) {
                    errors.fuelTypeError = L('Please select year of production.');
                }
            }
        } else if(!stopChecking) {
            errors.yearError = L('Please select year of production.');
            stopChecking = true;
        }

        // engine capacity
        if(fuelTypeInput && fuelTypeInput.length > 0) {
            if(engineCapacity[vehicleTypeId] && engineCapacity[vehicleTypeId][vehicleBrandId] && engineCapacity[vehicleTypeId][vehicleBrandId][productionYear] && engineCapacity[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput] && engineCapacity[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput].length > 0 && engineCapacity['error'].length === 0 && engineCapacity['errorsCount'] < maxErrorsCount) {
                engineCapacity[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput].forEach((engineCapacity: any, index: number) => {
                    if(engineCapacity === null) {
                        allowVehicleConfigWithoutAllData = true;
                    } else {
                        engineCapacityOptions.push({key: engineCapacity, text: engineCapacity.toString()});
                    }
                })
            } else {
                if((fuelTypeInput && fuelTypeInput.length > 0 && customInputsPrevData['fuelType'] !== fuelTypeInput) && vehicleConfiguration['errorsCount'] < maxErrorsCount && vehicleConfiguration['error'].length === 0) {
                    this.toggleCustomInputsAsyncActionInProgressFlag(true);
                    this.getDataForCustomInputs("getEngineCapacity", vehicleTypeId, vehicleBrandId, productionYear, fuelTypeInput);
                } else {
                    errors.eurotaxInfoekspertError = L(vehicleConfiguration['error']);
                }
            }
        } else if(!stopChecking) {
            errors.engineCapacityError = L('Please select vehicle fuel type.');
            stopChecking = true;
        }

        // vehicle model
        if(engineCapacityInput && engineCapacityInput > 0) {
            if(vehicleModels[vehicleTypeId] && vehicleModels[vehicleTypeId][vehicleBrandId] && vehicleModels[vehicleTypeId][vehicleBrandId][productionYear] && vehicleModels[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput] && vehicleModels[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput] && vehicleModels[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput].length > 0 && vehicleModels['error'].length === 0 && vehicleModels['errorsCount'] < maxErrorsCount) {
                vehicleModels[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput].forEach((model: any, index: number) =>{
                    modelOptions.push({key: model.key, text: model.translatedName});
                });
            } else {
                if((engineCapacityInput && engineCapacityInput > 0 && customInputsPrevData['engineCapacity'] !== engineCapacityInput) && vehicleModels['errorsCount'] < maxErrorsCount && vehicleModels['error'].length === 0) {
                    this.toggleCustomInputsAsyncActionInProgressFlag(true);
                    this.getDataForCustomInputs("getModels", vehicleTypeId, vehicleBrandId, productionYear, fuelTypeInput, engineCapacityInput);
                } else {
                    errors.modelError = L("Please select engine capacity.");
                }
            }
        } else if(!stopChecking) {
            errors.modelError = L("Please select engine capacity.");
            stopChecking = true;
        }

        let allowVehicleConfigWithoutAllData: boolean = false;

        // engine power
        if(vehicleModelId && vehicleModelId.length > 0) {
            if(enginePower[vehicleTypeId] && enginePower[vehicleTypeId][vehicleBrandId] && enginePower[vehicleTypeId][vehicleBrandId][productionYear] && enginePower[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput] && enginePower[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput] && enginePower[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId] && enginePower[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId].length > 0 && enginePower['error'].length === 0 && enginePower['errorsCount'] < maxErrorsCount) {
                enginePower[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId].forEach((enginePower: any, index: number) => {
                    if(enginePower === null) {
                        allowVehicleConfigWithoutAllData = true;
                    } else {
                        enginePowerOptions.push({key: enginePower, text: enginePower.toString()});
                    }
                });
            } else {
                if((vehicleModelId && vehicleModelId.length > 0 && customInputsPrevData['vehicleModelId'] !== vehicleModelId) && vehicleConfiguration['errorsCount'] < maxErrorsCount && vehicleConfiguration['error'].length === 0) {
                    this.toggleCustomInputsAsyncActionInProgressFlag(true);
                    this.getDataForCustomInputs("getEnginePower", vehicleTypeId, vehicleBrandId, productionYear, fuelTypeInput, engineCapacityInput, vehicleModelId);
                } else {
                    errors.eurotaxInfoekspertError = L(vehicleConfiguration['error']);
                }
            }
        } else if(!stopChecking) {
            errors.enginePowerError = L('Please select vehicle model.');
            stopChecking = true;
        }

        // vehicle configuration
        if((enginePowerInput && enginePowerInput > 0) || allowVehicleConfigWithoutAllData) {
            if(((vehicleConfiguration[vehicleTypeId] && vehicleConfiguration[vehicleTypeId][vehicleBrandId] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId][enginePowerInput]) ||
                (vehicleConfiguration[vehicleTypeId] && vehicleConfiguration[vehicleTypeId][vehicleBrandId] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput] && allowVehicleConfigWithoutAllData))
                && vehicleConfiguration['error'].length === 0 && vehicleConfiguration['errorsCount'] < maxErrorsCount
            ) {
                if(vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId][enginePowerInput]['euroTaxs']) {
                    vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId][enginePowerInput]['euroTaxs'].forEach((configuration: any, index: number) => {
                        configurationOptions.eurotax.push({ key: configuration.id, text: configuration.name });
                    });
                }
                // if(vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId][enginePowerInput]['infoExperts']) {
                //     vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId][enginePowerInput]['infoExperts'].forEach((configuration: any, index: number) => {
                //         configurationOptions.infoekspert.push({ key: configuration.id, text: configuration.name });
                //     });
                // }

                let eurotaxSet: boolean = false;
                let infoEkspertSet: boolean = false;

                if(vehicleConfigurationEurotaxId && vehicleConfigurationEurotaxId.length > 0) {
                    if(customInputsPrevData['vehicleConfigurationEurotaxId'] !== vehicleConfigurationEurotaxId) {
                        this.setState((prevState) => ({...prevState, operationEurotaxSuccess: true, 
                            customInputsPrevData: {...customInputsPrevData,
                                vehicleConfigurationEurotaxId: vehicleConfigurationEurotaxId,
                            }
                        }));

                        this.props.onInputChange(this.props.mapKeyToId("mapAttributeNameToId", "eurotaxId"), vehicleConfigurationEurotaxId.toString());
                        this.props.onInputChange(this.props.mapKeyToId("mapAttributeNameToId", "findAVehicle"), L('Success! Eurotax ID is now set.'));
                    }

                    eurotaxSet = true;
                }

                // if(vehicleConfigurationInfoExpertId && vehicleConfigurationInfoExpertId.length > 0) {
                //     if(customInputsPrevData['vehicleConfigurationInfoExpertId'] !== vehicleConfigurationInfoExpertId) {
                //         this.setState((prevState) => ({...prevState, operationInfoEkspertSuccess: true,
                //             customInputsPrevData: {...customInputsPrevData,
                //                 vehicleConfigurationInfoExpertId: vehicleConfigurationInfoExpertId,
                //             }
                //         }));
                //         this.props.onInputChange(this.props.mapKeyToId("mapAttributeNameToId", "expertInfoId"), vehicleConfigurationInfoExpertId.toString());
                //         this.props.onInputChange(this.props.mapKeyToId("mapAttributeNameToId", "findAVehicle"), L("Success! Expert's information ID is now set."));
                //     }

                //     infoEkspertSet = true;
                // }
                
                if(eurotaxSet && infoEkspertSet && !bothSet) {
                    this.setState((prevState) => ({...prevState, bothSet: true}));
                    this.props.onInputChange(this.props.mapKeyToId("mapAttributeNameToId", "findAVehicle"), L("Eurotax ID and Expert's information ID is now set."));
                }
            } else {
                if(((enginePowerInput && enginePowerInput > 0 && customInputsPrevData['enginePower'] !== enginePowerInput) || allowVehicleConfigWithoutAllData) && vehicleConfiguration['errorsCount'] < maxErrorsCount && vehicleConfiguration['error'].length === 0) {
                    this.toggleCustomInputsAsyncActionInProgressFlag(true);
                    this.getDataForCustomInputs("getConfiguration", vehicleTypeId, vehicleBrandId, productionYear, fuelTypeInput, engineCapacityInput, vehicleModelId, enginePowerInput);
                } else {
                    errors.eurotaxInfoekspertError = L(vehicleConfiguration['error']);
                }
            }
        } else if(!stopChecking && configurationOptions.eurotax.length === 0) {
            errors.eurotaxError = L("Please select engine power.");
            stopChecking = true;
        }

        // vehicle configuration - infoEkspert codes
        if((vehicleConfigurationEurotaxId && vehicleConfigurationEurotaxId.length > 0) || allowVehicleConfigWithoutAllData) {
            if(((vehicleConfiguration[vehicleTypeId] && vehicleConfiguration[vehicleTypeId][vehicleBrandId] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId][enginePowerInput] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId][enginePowerInput]['infoExperts']) ||
                (vehicleConfiguration[vehicleTypeId] && vehicleConfiguration[vehicleTypeId][vehicleBrandId] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId][enginePowerInput]['infoExperts'] && allowVehicleConfigWithoutAllData))
                && vehicleConfiguration['error'].length === 0 && vehicleConfiguration['errorsCount'] < maxErrorsCount
                && customInputsPrevData['vehicleConfigurationEurotaxId'] === vehicleConfigurationEurotaxId
            ) {
                if(configurationOptions.infoekspert.length === 0) {
                    vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId][enginePowerInput]['infoExperts'].forEach((configuration: any, index: number) => {
                        configurationOptions.infoekspert.push({ key: configuration.id, text: configuration.name });
                    });
                }

                if(vehicleConfigurationInfoExpertId && vehicleConfigurationInfoExpertId.length > 0) {
                    if(customInputsPrevData['vehicleConfigurationInfoExpertId'] !== vehicleConfigurationInfoExpertId) {
                        this.setState((prevState) => ({...prevState, operationInfoEkspertSuccess: true,
                            customInputsPrevData: {...customInputsPrevData,
                                vehicleConfigurationInfoExpertId: vehicleConfigurationInfoExpertId,
                                bothSet: true
                            }
                        }));
                        this.props.onInputChange("vehicleConfigurationInfoExpertId", vehicleConfigurationInfoExpertId.toString());
                        this.props.onInputChange(this.props.mapKeyToId("mapAttributeNameToId", "expertInfoId"), vehicleConfigurationInfoExpertId.toString());
                        // this.props.onInputChange(this.props.mapKeyToId("mapAttributeNameToId", "findAVehicle"), L("Success! Expert's information ID is now set."));
                        this.props.onInputChange(this.props.mapKeyToId("mapAttributeNameToId", "findAVehicle"), L("Eurotax ID and Expert's information ID is now set."));
                    }
                } else if(configurationOptions.infoekspert.length === 1 && customInputsPrevData['vehicleConfigurationInfoExpertId'] !== configurationOptions.infoekspert[0].key) {
                    this.setState((prevState) => ({...prevState, operationInfoEkspertSuccess: true,
                        customInputsPrevData: {...customInputsPrevData,
                            vehicleConfigurationInfoExpertId: configurationOptions.infoekspert[0].key,
                            bothSet: true
                        }
                    }));

                    this.props.onInputChange("vehicleConfigurationInfoExpertId", configurationOptions.infoekspert[0].key.toString());
                    this.props.onInputChange(this.props.mapKeyToId("mapAttributeNameToId", "expertInfoId"), configurationOptions.infoekspert[0].key.toString());
                    // this.props.onInputChange(this.props.mapKeyToId("mapAttributeNameToId", "findAVehicle"), L("Success! Expert's information ID is now set."));
                    this.props.onInputChange(this.props.mapKeyToId("mapAttributeNameToId", "findAVehicle"), L("Eurotax ID and Expert's information ID is now set."));
                }
            } else {
                if(((productionYear && productionYear > 0) || allowVehicleConfigWithoutAllData) && vehicleConfiguration['errorsCount'] < maxErrorsCount && vehicleConfiguration['error'].length === 0) {
                    this.toggleCustomInputsAsyncActionInProgressFlag(true);
                    this.getDataForCustomInputs("getInfoEkspertCodes", vehicleTypeId, vehicleBrandId, productionYear, fuelTypeInput, engineCapacityInput, vehicleModelId, enginePowerInput, vehicleConfigurationEurotaxId);
                } else {
                    errors.eurotaxInfoekspertError = L(vehicleConfiguration['error']);
                }
            }
        } else if(!stopChecking) {
            if(!vehicleConfigurationEurotaxId || vehicleConfigurationEurotaxId.length === 0) {
                errors.infoekspertError = L("Please select eurotax code.");
            }
            stopChecking = true;
        }

        return <div className={`${classNames.customInputsWrapper} ${this.props.showCustomInputsBox ? '' : classNames.hide}`}>
            <Icon iconName="ChromeClose" onClick={() => this.props.onToggleClose()} title={L('Close')} className={classNames.closeIcon} />
            
            <MessageBar messageBarType={MessageBarType.info} isMultiline={false} className={classNames.messageBar}>
                {L('Enter vehicle details to find Eurotax ID and Expert Info ID.')}
            </MessageBar>

            { customInputsAsyncActionInProgress && <Spinner label={L('Collecting data...')} className={classNames.loadSpinner} size={SpinnerSize.medium} ariaLive="assertive" labelPosition="right" /> }

            <ComboBox
                label={L("Select vehicle type")}
                allowFreeform={false}
                autoComplete={'off'}
                options={typeOptions}
                className={classNames.comboBoxStyles}
                key={`selectType`}
                errorMessage={errors.typeError}
                disabled={typeOptions.length <= 0 || customInputsAsyncActionInProgress}
                selectedKey={!!vehicleTypeId ? vehicleTypeId : null}
                onChange={(e: any, value: any) => {
                    this.props.onInputChange(this.props.mapKeyToId("mapAttributeNameToId", "vehicleType"), this.props.mapKeyToId("mapAttributeValueToOptionId", value.key));
                    this.props.onInputChange('vehicleTypeId', value.key);
                }}
            />

            <ComboBox
                label={L("Select vehicle brand")}
                allowFreeform={false}
                autoComplete={'on'}
                options={brandOptions}
                className={classNames.comboBoxStyles}
                key={`selectBrand-${vehicleTypeId}`}
                errorMessage={errors.brandError}
                disabled={brandOptions.length <= 0 || customInputsAsyncActionInProgress}
                selectedKey={!!vehicleBrandId ? vehicleBrandId : null}
                onChange={(e: any, value: any) => this.props.onInputChange('vehicleBrandId', value.key)}
            />

            <TextField
                key={`productionYear`}
                type="number"
                label={L("Enter year of production")}
                errorMessage={errors.yearError}
                className={classNames.comboBoxStyles}
                disabled={!vehicleBrandSelected || customInputsAsyncActionInProgress}
                value={this.state.valueBeforeDebounce['productionYear']}
                onChange={(e: any, value: any) => {
                    this.setState((prevState) => ({...prevState, valueBeforeDebounce: {...this.state.valueBeforeDebounce, 'productionYear': value } }));
                    this.debouncedOnInputChange('productionYear', parseInt(value))
                }}
            />

            <ComboBox
                key={`selectFuelType-${vehicleTypeId}-${vehicleBrandId}`}
                label={L("Select fuel type")}
                allowFreeform={false}
                autoComplete={'on'}
                options={fuelTypeOptions}
                className={classNames.comboBoxStyles}
                errorMessage={errors.fuelTypeError}
                disabled={fuelTypeOptions.length <= 0 || customInputsAsyncActionInProgress}
                selectedKey={!!fuelTypeInput ? fuelTypeInput : null}
                onChange={(e: any, value: any) => {
                    this.props.onInputChange(this.props.mapKeyToId("mapAttributeNameToId", "fuelType"), this.props.mapKeyToId("mapAttributeValueToOptionId", value.key));
                    this.props.onInputChange('fuelType', value.key);
                }}
            />
            
            <ComboBox
                label={L("Select engine capacity")}
                key={`selectEngineCapacity-${vehicleTypeId}-${vehicleBrandId}-${fuelType}-${vehicleModelId}`}
                allowFreeform={false}
                autoComplete={'on'}
                options={engineCapacityOptions}
                className={classNames.comboBoxStyles}
                errorMessage={errors.engineCapacityError}
                disabled={engineCapacityOptions.length <= 0 || customInputsAsyncActionInProgress}
                selectedKey={!!engineCapacityInput ? engineCapacityInput : null}
                onChange={(e: any, value: any) => this.props.onInputChange('engineCapacity', value.key)}
            />

            <ComboBox
                label={L("Select vehicle model")}
                allowFreeform={false}
                autoComplete={'on'}
                options={modelOptions}
                className={classNames.comboBoxStyles}
                key={`selectModel-${vehicleTypeId}-${vehicleBrandId}-${fuelType}`}
                errorMessage={errors.modelError}
                disabled={modelOptions.length <= 0 || customInputsAsyncActionInProgress}
                selectedKey={!!vehicleModelId ? vehicleModelId : null}
                onChange={(e: any, value: any) => this.props.onInputChange('vehicleModelId', value.key)}
            />

            <ComboBox
                label={L("Select engine power (kW)")}
                key={`selectEnginePower-${vehicleTypeId}-${vehicleBrandId}-${fuelType}-${vehicleModelId}-${engineCapacity}`}
                allowFreeform={false}
                autoComplete={'on'}
                options={enginePowerOptions}
                className={classNames.comboBoxStyles}
                errorMessage={errors.enginePowerError}
                disabled={enginePowerOptions.length <= 0 || customInputsAsyncActionInProgress}
                selectedKey={!!enginePowerInput ? enginePowerInput : null}
                onChange={(e: any, value: any) => {
                    this.setState({bothSet: false})
                    this.props.onInputChange('enginePower', value.key)
                }}
            />

            <ComboBox
                label={L("Select vehicle configuration (Eurotax)")}
                allowFreeform={false}
                autoComplete={'on'}
                options={configurationOptions.eurotax}
                className={classNames.comboBoxStyles}
                key={`selectEurotaxConfiguration-${vehicleTypeId}-${vehicleBrandId}-${fuelType}-${vehicleModelId}-${engineCapacity}-${enginePower}`}
                errorMessage={errors.eurotaxError}
                disabled={configurationOptions.eurotax.length <= 0 || customInputsAsyncActionInProgress}
                selectedKey={!!vehicleConfigurationEurotaxId ? vehicleConfigurationEurotaxId : null}
                onChange={(e: any, value: any) => {
                    this.props.onInputChange('vehicleConfigurationEurotaxId', value.key);
                    this.props.onInputChange('vehicleConfigurationEurotaxName', value.text);
                    this.setEurotaxInfoexpertFormData('eurotaxCarName', value.text);
                }}
            />

            <ComboBox
                label={L("Vehicle configuration (InfoEkspert)")}
                options={configurationOptions.infoekspert}
                className={classNames.comboBoxStyles}
                key={`selectInfoEkspertConfiguration-${vehicleTypeId}-${vehicleBrandId}-${fuelType}-${vehicleModelId}-${engineCapacity}-${enginePower}`}
                errorMessage={errors.infoekspertError}
                disabled={true}
                selectedKey={!!vehicleConfigurationInfoExpertId ? 
                    (typeof vehicleConfigurationInfoExpertId !== 'number' ? parseInt(vehicleConfigurationInfoExpertId) : vehicleConfigurationInfoExpertId)
                    : null}
                onChange={(e: any, value: any) => {
                    this.props.onInputChange('vehicleConfigurationInfoExpertId', value.key);
                    this.props.onInputChange('vehicleConfigurationInfoExpertName', value.text);
                    this.setEurotaxInfoexpertFormData('infoExpertName', value.text);
                }}
            />

            { operationEurotaxSuccess && 
                <MessageBar messageBarType={MessageBarType.success} isMultiline={false} className={`${classNames.messageBar} ${classNames.messageBarMargin}`}
                    onDismiss={() => this.setState((prevState) => ({...prevState, operationEurotaxSuccess: false })) } >
                    { L('Success! Eurotax ID is now set.') }
                </MessageBar> }
            { operationInfoEkspertSuccess && 
                <MessageBar messageBarType={MessageBarType.success} isMultiline={false} className={`${classNames.messageBar} ${classNames.messageBarMargin}`}
                    onDismiss={() => this.setState((prevState) => ({...prevState, operationInfoEkspertSuccess: false })) } >
                    { L("Success! Expert's information ID is now set.") }
                </MessageBar> }
                
            { errors.eurotaxInfoekspertError.length > 0 && 
                <MessageBar messageBarType={MessageBarType.error} isMultiline={false} className={`${classNames.messageBar} ${classNames.messageBarMargin}`}>
                    {errors.eurotaxInfoekspertError}
                </MessageBar> }

            { customInputsAsyncActionInProgress && <Spinner label={L('Collecting data...')} className={classNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="right" /> }
        </div>;
    }
}