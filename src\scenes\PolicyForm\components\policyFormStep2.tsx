import { DefaultButton, FocusZone, FocusZoneDirection, FocusZoneTabbableElements, FontWeights, IButtonStyles, IChoiceGroupOption, IconButton, IDropdownOption, IIconProps, mergeStyleSets, Modal, Pivot, PivotItem, Selection, SelectionMode } from "@fluentui/react";
import React from "react";
import { LabeledTextField } from "../../../components/LabeledTextField";
import { L } from "../../../lib/abpUtility";
import AppConfig from "../../../lib/appconfig";
import AppConsts from "../../../lib/appconst";
import { InsurerDto } from "../../../services/insurer/dto/insurerDto";
import {additionalTheme, myTheme} from "../../../styles/theme";
import { getDropdownOptionsFromDataSource, getInputIconData, getInputTableData, getInputValidationData, isInputInTab, shouldSaveAsTemplateForCalculationAdjust, shouldSaveAsTemplateForTable, isCountriesInput, inputHasUserFieldKey } from "../../../utils/inputUtils";
import { getAbpLanguage, getLocaleName } from "../../../utils/languageUtils";
import { analyzeAttributeDependencies, conditionalAttribute, createInputsStateKey, renderElement } from "../../../utils/policyCalculationUtils";
import { generateDropdownOptionsIfCountriesInput } from "../../../utils/storeUtils";
import { filterBySome, isJsonString } from "../../../utils/utils";
import { ApkListBase } from "../../BaseComponents/apkListBase";
import { ContentViewModelProperty } from "../../BaseComponents/contentViewBase";
import { Controls } from "../../BaseComponents/controls";
import { DropdownBase } from "../../BaseComponents/dropdownBase";
import { VehicleListBase } from "../../BaseComponents/vehicleListBase";
import { EurotaxExpertInfoMixedCustomInputsBox } from "../../PolicyCalculation/components/eurotaxExpertInfoMixedCustomInputsBox";
import { DatePickerBase } from "../../BaseComponents/datePickerBase";
import { ApkAttachedFilesDto } from "../../../services/apkAttachedFiles/apkAttachedFilesDto";
import moment from "moment";

var _ = require('lodash');

const cancelIcon: IIconProps = { iconName: 'Cancel' };

const iconButtonStyles: Partial<IButtonStyles> = {
    root: {
        color: myTheme.palette.neutralPrimary,
        marginLeft: 'auto',
        marginTop: '4px',
        marginRight: '2px',
    },
    rootHovered: {
        color: myTheme.palette.neutralDark,
    },
};

const classNames = mergeStyleSets({
    hide: {
        display: 'none !important',
    },
    toolbar: {
        selectors: {
            '& .ms-Pivot': {
                display: 'none',
            }
        }
    },
    messageBar: {
        width: 'fit-content'
    },
    loadSpinner: {
        display: 'inline-flex',
        marginTop: '15px',
        marginLeft: 'auto',
        marginRight: 'auto',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    loadSpinnerTopBar: {
        display: 'inline-flex',
        marginLeft: '15px',
        marginRight: 'auto',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    toggleSlider: {
        margin: '15px 0',
        display: 'flex',
        alignContent: 'center',
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        border: `1px solid ${myTheme.palette.neutralSecondaryAlt}`,
        padding: '1px 16px',
        borderRadius: '2px',
        transition: 'background 150ms',
        background: myTheme.palette.white,
        selectors: {
            '& .ms-Slider-container': {
                cursor: 'grab',
                width: '40px',
                marginLeft: '10px',
            }
        }
    },
    toggleSliderActive: {
        background: myTheme.palette.themeLight,
    },
    container: {
        display: 'flex',
        flexFlow: 'column nowrap',
        alignItems: 'stretch',
        justifyContent: 'center',
        width: '75%',
        overflowX: 'hidden',
    },
    header: [
        myTheme.fonts.large,
        {
            flex: '1 1 auto',
            color: myTheme.palette.neutralPrimary,
            display: 'flex',
            alignItems: 'center',
            fontWeight: FontWeights.semibold,
            padding: '12px 12px 14px 24px',
        },
    ],
    contentContainer: {
        width: '100%',
        height: 'auto',
        padding: '0 20px 40px 20px',
        boxSizing: 'border-box',
        selectors: {
            '> div': {
                marginRight: '0',
                width: '99.9%',
                maxWidth: '99.9%',
                selectors: {
                    '> div': {
                        width: '99.9%',
                        maxWidth: '99.9%',
                    }
                }
            }
        }
    },
    pivotItemsContainer: {
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
        marginTop: '30px !important',
        marginBottom: '20px',
        minWidth: '30%',
        maxWidth: '1200px',
        minHeight: '150px',
        padding: '35px',
        boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.25)',
        borderRadius: '12px',
        selectors: {
            ':first-child': {
                marginTop: '25px',
            }
        }
    },
    modalActionButton: {
        display: 'block',
        margin: '30px auto 0',
    },
    myTabItem: {
        display: 'flex'
    }
});

export interface IPolicyFormStep2Props {
    countryStore: any;
    sportDisciplineStore: any;
    vehicleStore: any;
    apkAttachedFilesStore: any;
    insurerStore: any;
    product: any;
    isDataLoaded: boolean;
    inputsTypeValuePairs: any;
    inputsIdUserFieldsPairs: any;
    gnLanguage: any;
    customInputsData: any;
    isFastCalculation: boolean;
    inputErrors: number;
    allUserFields: any[];
    isEditMode: boolean;
    tempSelectedVehicle: string;
    tempSelectedApk: ApkAttachedFilesDto | undefined;
    inputsChangedManually: any;
    inputValuePairsStringified: string;
    asyncActionInProgress?: boolean;
    history: any;
    savedTemplateInputsForTable: any;
    productAttributes: any;
    insurers: any;
    savedMappedIdsForLaterUse: any;
    changeAdjustInputsChangedManually: (id: string, value: any) => void;
    setInputErrors: (errorsCount: number) => void;
    setIsFastCalculation: (value: boolean) => void;
    saveInputsForCalculationAdjust: (inputs: any) => void;
    onInputChange: (id: string, value: any, userFields: any) => void;
    onMassInputChange: (inputFields: any, userFields: any) => void;
    toggleAsyncActionFlag: (newState: boolean, forceUpdate: boolean) => void;
    mapKeyToId: (mapType: string, key: string) => string;
    onTempVehicleSelect: (tempVehicle: any, justId: boolean) => void;
    onTempApkSelect: (tempApk: any, justId: boolean) => void;
    setEurotaxInfoexpertFormData: (id: string, value: string) => void;
    formTabSwitch: (func: () => void) => void;
    onTabChange?: (selectedKey: string) => void;
    setSavedTemplateInputsForTable: (value: any) => void;
    getVehicleConfigByVin: (vin: string) => void;
    getVehicleConfigByRegistrationNumber: (registrationNumber: string) => void;
}

export class PolicyFormStep2 extends React.Component<IPolicyFormStep2Props> {
    private tabKeyIndexHelper: any = {};
    private tabs: any[] = [
        // { key: UserField.Key, name: displayName, items: [{value, order}, {value, order}] }
    ];
    private tabsParsed: boolean = false;
    private initialReRender: boolean = false;
    private inputsToSet: any[] = [];
    private showExpertInfoCustomInputsBox: boolean = false;
    private showEurotaxCustomInputsBox: boolean = false;
    private showEurotaxExpertInfoMixedCustomInputsBox: boolean = false;
    private fillingExampleDataActionInProgress: boolean = false;
    private exampleDataLoadedFlag: boolean = false;
    private templateInputsForTable: any = {};
    private templateInputsForCalculationAdjust: any = {};
    private tempInputIdUserFieldPairs: any = {};
    private prevTempInputErrors: number = 0;
    private tempInputErrors: number = 0;
    private tabInputsInEditModeSet: boolean = false;
    private isVehicleListModalOpen: boolean = false;
    private isApkListModalOpen: boolean = false;
    private savedAttributeOptions: any = {};
    private lastRenderTimestamp: number = 0;
    private tabsSelectedKey: string = '0';
    private lastSetDefaultValueTimestamp: number = 0;
    private conditionalDefaultValueAlreadySetFor: any[] = [];
    private savedAttributes: any = {};
    private prevInputValuePairsStringified: string = '';
    private prevAsyncActionInprogress: boolean | undefined = undefined;
    private selectCountryMappedInputId: string = '';
    private savedMappedIdsForLaterUse: any = {} ;
    private insurersDropdownOptions: IDropdownOption[] = [];
    private onTabChange?: (selectedKey: string) => void;
    private _countryListSelection: Selection = new Selection({
        onSelectionChanged: () => {
            const selectedCountry: any = this._countryListSelection.getSelection();
            if(Array.isArray(selectedCountry) && selectedCountry.length > 0 && selectedCountry[0].code) {
                if(this.selectCountryMappedInputId.length === 0) this.selectCountryMappedInputId = this.props.mapKeyToId("mapAttributeNameToId", "travelCountryList");
                const currentValue = this.props.inputsTypeValuePairs[this.selectCountryMappedInputId] && isJsonString(this.props.inputsTypeValuePairs[this.selectCountryMappedInputId]) && Array.isArray(JSON.parse(this.props.inputsTypeValuePairs[this.selectCountryMappedInputId])) ?
                                        JSON.parse(this.props.inputsTypeValuePairs[this.selectCountryMappedInputId]) : this.props.inputsTypeValuePairs[this.selectCountryMappedInputId];
                const newObj: any[] = currentValue ? [...currentValue] : [];
                const newObjNames: string[] = newObj.map((country: any) => country.name);

                if(!newObjNames.includes(selectedCountry[0].name)) {
                    this.props.onInputChange(this.selectCountryMappedInputId, JSON.stringify([...newObj, selectedCountry[0]]), 
                                                this.tempInputIdUserFieldPairs && this.tempInputIdUserFieldPairs[this.selectCountryMappedInputId]);
                }
                this._countryListSelection.setAllSelected(false);
            }
            this.forceUpdate();
        },
        selectionMode: SelectionMode.single
    });
    private selectSportMappedInputId: string = '';
    private _sportListSelection: Selection = new Selection({
        onSelectionChanged: () => {
            const selectedSport: any = this._sportListSelection.getSelection();
            if(Array.isArray(selectedSport) && selectedSport.length > 0 && selectedSport[0].id) {
                if(this.selectSportMappedInputId.length === 0) this.selectSportMappedInputId = this.props.mapKeyToId("mapAttributeNameToId", "travelSportList");
                const currentValue = this.props.inputsTypeValuePairs[this.selectSportMappedInputId] && isJsonString(this.props.inputsTypeValuePairs[this.selectSportMappedInputId]) && Array.isArray(JSON.parse(this.props.inputsTypeValuePairs[this.selectSportMappedInputId])) ?
                                        JSON.parse(this.props.inputsTypeValuePairs[this.selectSportMappedInputId]) : this.props.inputsTypeValuePairs[this.selectSportMappedInputId];
                const newObj: any[] = currentValue ? [...currentValue] : [];
                const newObjIds: number[] = newObj.map((sport: any) => sport.id);

                if(!newObjIds.includes(selectedSport[0].id)) {
                    // for multiple values: JSON.stringify([...newObj, selectedSport[0]])
                    this.props.onInputChange(this.selectSportMappedInputId, JSON.stringify([selectedSport[0]]), 
                                                this.tempInputIdUserFieldPairs && this.tempInputIdUserFieldPairs[this.selectSportMappedInputId]);
                }
                this._sportListSelection.setAllSelected(false);
            }
            this.forceUpdate();
        },
        selectionMode: SelectionMode.single
    });
    private _apkListSelection: Selection = new Selection({
        onSelectionChanged: () => {
            const selectedApk: any = this._apkListSelection.getSelection();
            if(Array.isArray(selectedApk) && selectedApk.length > 0 && !!selectedApk[0].id) {
                this.props.onTempApkSelect(selectedApk[0], false);
                this._apkListSelection.setAllSelected(false);
            }
            this.forceUpdate();
        },
        selectionMode: SelectionMode.single
    });
    private debouncedOnInputChange: any = _.debounce((inputKey: string, value: any, userFields: any, customPayload?: any) => {
        this.props.onInputChange(inputKey, value, userFields);

        this.props.changeAdjustInputsChangedManually(inputKey, value);
    }, AppConsts.defaultInputsDelay, []);
    private tabInsurerInput: any;
    private tabPolicyNumberInput: any;
    private tabPolicyTotalInput: any;
    private tabEndDateInput: any;
    private tabPolicyDateInput: any;
    private conditionalAttributeCache: Map<string, any> = new Map();
    private inputDependencies: Map<string, Set<string>> = new Map();
    private keyIdPairs: Map<string, string> = new Map();

    constructor(props: any) {
        super(props);
        this.tabInsurerInput = React.createRef();
        this.tabPolicyNumberInput = React.createRef();
        this.tabPolicyTotalInput = React.createRef();
        this.tabEndDateInput = React.createRef();
        this.tabPolicyDateInput = React.createRef();
        this.focusTabPolicyNumberInput = this.focusTabPolicyNumberInput.bind(this)
        this.focusTabPolicyTotalInput = this.focusTabPolicyTotalInput.bind(this);
        this.focusTabInsurerInput = this.focusTabInsurerInput.bind(this);
        this.focusTabEndDateInput = this.focusTabEndDateInput.bind(this);
        this.focusTabPolicyDateInput = this.focusTabPolicyDateInput.bind(this);
    }
    focusTabPolicyDateInput() {
        this.tabPolicyDateInput.current.focus();
    }

    focusTabEndDateInput() {
        this.tabEndDateInput.current.focus();
    }

    focusTabPolicyTotalInput() {
        this.tabPolicyTotalInput.current.focus();
    }

    focusTabPolicyNumberInput() {
        this.tabPolicyNumberInput.current.focus();
    }

    focusTabInsurerInput() {
        this.tabInsurerInput.current.focus();
    }
    
    componentDidMount() {
        this.setDefaultInputsData();

        this.selectCountryMappedInputId = this.props.mapKeyToId("mapAttributeNameToId", "travelCountryList");

        if(!this.initialReRender) {
            this.initialReRender = true;
            this.forceUpdate();
        }

        this.props.formTabSwitch(this.handleTabSwitch);
    }

    private getVehicleConfigByVin() {
        this.props.getVehicleConfigByVin(this.props.inputsTypeValuePairs[this.props.savedMappedIdsForLaterUse.autoVin]);
    }

    private getVehicleConfigByRegistrationNumber() {
        this.props.getVehicleConfigByRegistrationNumber(this.props.inputsTypeValuePairs[this.props.savedMappedIdsForLaterUse.autoRegistrationNumber]);
    }

    private setDefaultInputsData() {
        if(this.inputsToSet.length > 0) {
            const cloneInputsTypeValuePairs = {};
            const cloneInputsIdUserFieldsPairs = {};
            
            this.inputsToSet.forEach((element) => {
                // this.props.onInputChange(element.id, element.value, this.tempInputIdUserFieldPairs[element.id]);
                cloneInputsTypeValuePairs[element.id] = element.value ? 
                                                        (isJsonString(element.value) ? 
                                                            JSON.parse(element.value) : element.value) 
                                                        : element.value;
                cloneInputsIdUserFieldsPairs[element.id] = this.tempInputIdUserFieldPairs[element.id];
            });

            this.props.onMassInputChange(cloneInputsTypeValuePairs, cloneInputsIdUserFieldsPairs);
            this.inputsToSet = [];
        }
    }

    private setConditionalDefaultInputsData(inputsToSet: any[]) {
        if(inputsToSet.length > 0) {
            inputsToSet.forEach((element) => {
                if(this.props.inputsChangedManually[element.id]) {
                    return;
                }
                this.props.onInputChange(element.id, element.value, element.userFields);
            });
            inputsToSet = [];
        }
    }

    // private async fillWithExampleData() {
    //     this.props.toggleAsyncActionFlag(true, true);
    //     this.fillingExampleDataActionInProgress = true;

    //     let type: string = "";

    //     if(this.props.product && this.props.product.SeName && this.props.product.SeName.length > 0) {
    //         type = this.props.mapKeyToId("mapProductIdToType", this.props.mapKeyToId("mapProductNameToProductId", this.props.product.SeName));

    //         if(type.length > 0) {
    //             await policyCalculationService.getFillExample(type).then((response) => {
    //                 if(response.data && response.data.data && response.data.data.length > 0) {
    //                     const exampleData = response.data.data;
    //                     const cloneInputsTypeValuePairs = {...this.props.inputsTypeValuePairs};
    //                     const cloneInputsIdUserFieldsPairs = {...this.props.inputsIdUserFieldsPairs};

    //                     for(let key in exampleData) {
    //                         if(exampleData.hasOwnProperty(key)) {
    //                             cloneInputsTypeValuePairs[exampleData[key].productAttributeId] = exampleData[key].valueId ? 
    //                                                                                 (isJsonString(exampleData[key].valueId) ? 
    //                                                                                     JSON.parse(exampleData[key].valueId) : exampleData[key].valueId) 
    //                                                                                 : exampleData[key].value;
    //                             cloneInputsIdUserFieldsPairs[exampleData[key].productAttributeId] = this.tempInputIdUserFieldPairs[exampleData[key].productAttributeId];                                            
    //                         }
    //                     }
                        
    //                     this.props.onMassInputChange(cloneInputsTypeValuePairs, cloneInputsIdUserFieldsPairs);
    //                 }
    //             });
    //         }

    //         this.exampleDataLoadedFlag = true;
    //         this.fillingExampleDataActionInProgress = false;
    //         this.props.toggleAsyncActionFlag(false, true);
    //     }
    // }

    private setTabs(UserFields: any) {
        let tabKeys: string[] = [];
        let tabNames: string[] = [];

        UserFields.forEach((UserField: any) => {    
            if(UserField.Key === "tabsName") {
                tabNames = UserField.Value.split(';;');
                tabNames.forEach((tabName, i) => {
                    let splittedTabNames = tabName.split(';');
                    splittedTabNames.forEach((splittedTabName) => {
                        let translatedNames = splittedTabName.split('=');
                        if(translatedNames[0].toLowerCase() === getAbpLanguage()) {
                            tabNames[i] = translatedNames[1];
                        }
                    });
                });
            } else if(UserField.Key === "tabs") {
                tabKeys = UserField.Value.split(';');
                if(tabNames.length <= 0) {
                    tabNames = tabKeys;
                }
            }
            
        });
        
        for(let i: number = 0; i < tabKeys.length; i++) {
            if(tabKeys[i].length > 0) {
                this.tabs.push({ key: tabKeys[i], name: tabNames[i], items: [], tabErrorsCount: 0});
                this.tabKeyIndexHelper[tabKeys[i]] = this.tabs.length - 1;
            }
        };
    }


    private parseTabs(UserFields: any, attributeElement: any, attrId: string, attributeId: string, updateValue: boolean, tabHasError?: boolean) {
        let tabUserField: any = {};
        let hideAttr: boolean = false;

        UserFields.forEach((UserField: any) => {
            if(UserField.Key === "Hide" && UserField.Value === "true") {
                hideAttr = true;
            }

            if(UserField.Key.substr(0, 4) === "step" && UserField.Key.substr(0, 5) !== "step_") {
                tabUserField = UserField;
            }
        });

        if(Number.isInteger(this.tabKeyIndexHelper[tabUserField.Key])) {
            const currentTabErrorCount: number = this.tabs[this.tabKeyIndexHelper[tabUserField.Key]].tabErrorsCount;
            let newTabErrorCount: number = tabHasError === true ? currentTabErrorCount + 1 : currentTabErrorCount;
            this.tabs[this.tabKeyIndexHelper[tabUserField.Key]].tabErrorsCount = newTabErrorCount;
        }

        if(Number.isInteger(this.tabKeyIndexHelper[tabUserField.Key]) && (!hideAttr || this.props.isEditMode) &&
            typeof attributeElement !== 'undefined' && this.tabKeyIndexHelper[tabUserField.Key] !== AppConfig.policyFormInsurerDataTabIndex
        ) {
            if(updateValue) {
                let attrUpdated = false;
                this.tabs[this.tabKeyIndexHelper[tabUserField.Key]].items.some((item: any) => {
                    if(item.id === attributeId) {
                        item.value = attributeElement;
                        attrUpdated = true;
                        return true;
                    }
                    return false;
                });

                if(!attrUpdated) {
                    this.tabs[this.tabKeyIndexHelper[tabUserField.Key]].items.push({ id: attributeId, value: attributeElement, order: tabUserField.Value, hide: hideAttr });
                }
            } else {
                this.tabs[this.tabKeyIndexHelper[tabUserField.Key]].items.push({ id: attributeId, value: attributeElement, order: tabUserField.Value, hide: hideAttr });
            }
        } else if(!attributeElement || this.tabKeyIndexHelper[tabUserField.Key] === AppConfig.policyFormInsurerDataTabIndex) {
            let indexToDelete = -1;

            this.tabs[this.tabKeyIndexHelper[tabUserField.Key]].items.some((item: any, index: number) => {
                if(item.id === attributeId) {
                    indexToDelete = index;
                    return true;
                }
                return false;
            });

            if(indexToDelete >= 0) {
                delete this.tabs[this.tabKeyIndexHelper[tabUserField.Key]].items[indexToDelete];
                delete this.props.inputsTypeValuePairs[attrId];
            }
        }
        // else { ### TODO create additional L("Other") tab and store there attributes without tab Key
        //     this.tabs.push({ name: UserField.Key, items: [{ value: attributeElement, order: UserField.Value }]});
        //     this.tabKeyIndexHelper[UserField.Key] = this.tabs.length - 1;
        // }
    }

    private deleteFromTab(ProductAttributeId: string) {
        if(this.tabs && this.tabs.length > 0) {
            this.tabs.some((tab: any, tabIndex: number) => {
                let itemFound: boolean = false;

                if(tab.items) {
                    tab.items.some((item: any, itemIndex: number) => {
                        if(item.id === ProductAttributeId) {
                            this.tabs[tabIndex].items.splice(itemIndex, 1);
                            itemFound = true;
                            return true;
                        }
                        return false;
                    });
                }

                if(itemFound) {
                    this.tabs[tabIndex].tabErrorsCount = this.tabs[tabIndex].tabErrorsCount > 0 ? this.tabs[tabIndex].tabErrorsCount - 1 : 0;
                    return true;
                }
                return false;
            });
        }
    }

    private sortByOrder(a: any, b: any) {
        a.order = parseInt(a.order);
        b.order = parseInt(b.order);

        if (a.order < b.order){
            return -1;
        }
        if (a.order > b.order){
            return 1;
        }
        return 0;
    }

    private toggleEurotaxExpertInfoMixedCustomInputsBox() {
        this.showEurotaxExpertInfoMixedCustomInputsBox = !this.showEurotaxExpertInfoMixedCustomInputsBox;
        if(this.showEurotaxExpertInfoMixedCustomInputsBox === true) {
            this.showEurotaxCustomInputsBox = false;
            this.showExpertInfoCustomInputsBox = false;
        }
        this.forceUpdate();
    }

    private toggleExpertInfoCustomInputsBox() {
        this.showExpertInfoCustomInputsBox = !this.showExpertInfoCustomInputsBox;
        if(this.showExpertInfoCustomInputsBox === true) {
            this.showEurotaxCustomInputsBox = false;
            this.showEurotaxExpertInfoMixedCustomInputsBox = false;
        }
        this.forceUpdate();
    }

    private toggleEurotaxCustomInputsBox() {
        this.showEurotaxCustomInputsBox = !this.showEurotaxCustomInputsBox;
        if(this.showEurotaxCustomInputsBox === true) {
            this.showExpertInfoCustomInputsBox = false;
            this.showEurotaxExpertInfoMixedCustomInputsBox = false;
        }
        this.forceUpdate();
    }

    private toggleVehicleListModal() {
        this.isVehicleListModalOpen = !this.isVehicleListModalOpen;
        this.forceUpdate();
    }

    private toggleApkListModal() {
        this.isApkListModalOpen = !this.isApkListModalOpen;
        this.forceUpdate();
    }

    private travelIncreaseNumberOfTravelers() {
        const travelTypeOfCalculationId = this.props.mapKeyToId("mapAttributeNameToId", "travelTypeOfCalculation");
        const NumberOfPeopleTravelingId: string = typeof this.props.inputsTypeValuePairs[travelTypeOfCalculationId] === 'undefined' ||
                    this.props.inputsTypeValuePairs[travelTypeOfCalculationId] === this.props.mapKeyToId("mapAttributeValueToOptionId", "TYPE_OF_CALCULATION_LIMITED") ? 
                    this.props.mapKeyToId("mapAttributeNameToId", "numberOfPeopleTraveling") : 
                    this.props.mapKeyToId("mapAttributeNameToId", "numberOfPeopleTravelingFullData");

        if(!!NumberOfPeopleTravelingId) {
            const currentValue: string | undefined = this.props.inputsTypeValuePairs[NumberOfPeopleTravelingId];
            if(currentValue && !!currentValue) {
                this.props.onInputChange(NumberOfPeopleTravelingId, (parseInt(currentValue) + 1).toString(), this.props.inputsIdUserFieldsPairs[NumberOfPeopleTravelingId]);
            } else {
                this.props.onInputChange(NumberOfPeopleTravelingId, '1', this.props.inputsIdUserFieldsPairs[NumberOfPeopleTravelingId]);
            }
        }
    }

    private travelReduceNumberOfTravelers() {
        const travelTypeOfCalculationId = this.props.mapKeyToId("mapAttributeNameToId", "travelTypeOfCalculation");
        const NumberOfPeopleTravelingId: string = typeof this.props.inputsTypeValuePairs[travelTypeOfCalculationId] === 'undefined' ||
                    this.props.inputsTypeValuePairs[travelTypeOfCalculationId] === this.props.mapKeyToId("mapAttributeValueToOptionId", "TYPE_OF_CALCULATION_LIMITED") ? 
                    this.props.mapKeyToId("mapAttributeNameToId", "numberOfPeopleTraveling") : 
                    this.props.mapKeyToId("mapAttributeNameToId", "numberOfPeopleTravelingFullData");

        if(!!NumberOfPeopleTravelingId) {
            const currentValue: string | undefined = this.props.inputsTypeValuePairs[NumberOfPeopleTravelingId];
            if(currentValue && !!currentValue) {
                this.props.onInputChange(NumberOfPeopleTravelingId, parseInt(currentValue) > 0 ? (parseInt(currentValue) - 1).toString() : '0', this.props.inputsIdUserFieldsPairs[NumberOfPeopleTravelingId]);
            } else {
                this.props.onInputChange(NumberOfPeopleTravelingId, '0', this.props.inputsIdUserFieldsPairs[NumberOfPeopleTravelingId]);
            }
        }
    }

    private cancelTravelIncreaseNumberOfTravelers() {
        const travelTypeOfCalculationId = this.props.mapKeyToId("mapAttributeNameToId", "cancelTravelTypeOfCalculation");
        const NumberOfPeopleTravelingId: string = typeof this.props.inputsTypeValuePairs[travelTypeOfCalculationId] === 'undefined' ||
                    this.props.inputsTypeValuePairs[travelTypeOfCalculationId] === this.props.mapKeyToId("mapAttributeValueToOptionId", "CANCEL_TRAVEL_TYPE_OF_CALCULATION_LIMITED") ? 
                    this.props.mapKeyToId("mapAttributeNameToId", "cancelNumberOfPeopleTraveling") : 
                    this.props.mapKeyToId("mapAttributeNameToId", "cancelNumberOfPeopleTraveling"); // cancelNumberOfPeopleTravelingFullData

        if(!!NumberOfPeopleTravelingId) {
            const currentValue: string | undefined = this.props.inputsTypeValuePairs[NumberOfPeopleTravelingId];
            if(currentValue && !!currentValue) {
                this.props.onInputChange(NumberOfPeopleTravelingId, (parseInt(currentValue) + 1).toString(), this.props.inputsIdUserFieldsPairs[NumberOfPeopleTravelingId]);
            } else {
                this.props.onInputChange(NumberOfPeopleTravelingId, '1', this.props.inputsIdUserFieldsPairs[NumberOfPeopleTravelingId]);
            }
        }
    }

    private cancelTravelReduceNumberOfTravelers() {
        const travelTypeOfCalculationId = this.props.mapKeyToId("mapAttributeNameToId", "cancelTravelTypeOfCalculation");
        const NumberOfPeopleTravelingId: string = typeof this.props.inputsTypeValuePairs[travelTypeOfCalculationId] === 'undefined' ||
                    this.props.inputsTypeValuePairs[travelTypeOfCalculationId] === this.props.mapKeyToId("mapAttributeValueToOptionId", "CANCEL_TRAVEL_TYPE_OF_CALCULATION_LIMITED") ? 
                    this.props.mapKeyToId("mapAttributeNameToId", "cancelNumberOfPeopleTraveling") : 
                    this.props.mapKeyToId("mapAttributeNameToId", "cancelNumberOfPeopleTraveling"); // cancelNumberOfPeopleTravelingFullData

        if(!!NumberOfPeopleTravelingId) {
            const currentValue: string | undefined = this.props.inputsTypeValuePairs[NumberOfPeopleTravelingId];
            if(currentValue && !!currentValue) {
                this.props.onInputChange(NumberOfPeopleTravelingId, parseInt(currentValue) > 0 ? (parseInt(currentValue) - 1).toString() : '0', this.props.inputsIdUserFieldsPairs[NumberOfPeopleTravelingId]);
            } else {
                this.props.onInputChange(NumberOfPeopleTravelingId, '0', this.props.inputsIdUserFieldsPairs[NumberOfPeopleTravelingId]);
            }
        }
    }

    private mapAttributes(): any {
        const {inputsTypeValuePairs, inputsIdUserFieldsPairs, productAttributes, savedMappedIdsForLaterUse, product, isDataLoaded, gnLanguage} = this.props;
        this.tempInputErrors = 0;

        let shouldMassUpdateInputs: boolean = false;
        let inputsToSet: any[] = [];
        let now = + new Date();

        const forbiddenIds = [
            savedMappedIdsForLaterUse.autoVehicleAverageAnnualMileage,
            savedMappedIdsForLaterUse.autoVehicleParkingPlace,
            savedMappedIdsForLaterUse.autoMaritalStatus,
            savedMappedIdsForLaterUse.autoVehicleChildrenUnder26,
            savedMappedIdsForLaterUse.autoYearOfPurchaseOfTheVehicle,
            savedMappedIdsForLaterUse.autoDrivingLicenceIssueYear
        ];

        let attributes: any = (product && product.ProductAttributeMappings ? product.ProductAttributeMappings!.map((attr: any, i: number) => {
            if (forbiddenIds.includes(attr.Id)) {
                return false;
            }
            let skipRender: boolean = false;
            let control: any;
            let options: any = {
                dropdown: [] as IDropdownOption[],
                choicegroup: [] as IChoiceGroupOption[],
                tableInputs: {} as any,
            };
            let textFieldRows = 1;
            let tempUserFields: any = null;
            let attribute: any = attr.ProductAttribute;
            let customControlTypeUserField: any;

            this.prevTempInputErrors = this.tempInputErrors;
            
            if(attr.DefaultValue && !inputsTypeValuePairs[attr.Id]) {
                this.inputsToSet.push({id: attr.Id, value: attr.DefaultValue});
            }

            attribute['attrId'] = attr.Id;

            if(attribute.UserFields) {
                tempUserFields = attribute.UserFields;
                customControlTypeUserField = tempUserFields.filter((userField: any) => userField.Key === 'customControlType');
                
                if(inputHasUserFieldKey(tempUserFields, "is_only_for_apk")) {
                    return false;
                }
            }

            let controlType: string = customControlTypeUserField && customControlTypeUserField.length > 0 ? customControlTypeUserField[0].Value : attr.AttributeControlTypeId;
            switch(controlType) {
                case "Datepicker":
                    control = Controls.Date;
                    break;
                case "Timepicker":
                    control = Controls.Time;
                    break;
                case "TextBox":
                    control = Controls.Text;
                    break;
                case "MultilineTextbox":
                    control = Controls.Text;
                    textFieldRows = 5;
                    break;
                case "RadioList":
                    control = Controls.ChoiceGroup;
                    options.choicegroup = attr.ProductAttributeValues.map((attrValue: any) => {
                        if(!inputsTypeValuePairs[attr.Id] && !this.inputsToSet.some(e => e.id === attr.Id) && attrValue.IsPreSelected && !this.tabsParsed) {
                            this.inputsToSet.push({id: attr.Id, value: attrValue.Id});
                        }

                        return { key: attrValue.Id, name: attrValue.Name,
                                text: attrValue.Locales && attrValue.Locales.length > 0 ? getLocaleName(attrValue.Locales, gnLanguage) : attrValue.Name, 
                                checked: inputsTypeValuePairs[attr.Id] && inputsTypeValuePairs[attr.Id] === attrValue.Id ? true : attrValue.IsPreSelected,
                                disabled: false }
                    }) as IChoiceGroupOption[];
                    break;
                case "Checkboxes":
                    control = Controls.CheckBoxOptions;
                    options.dropdown = attr.ProductAttributeValues.map((attrValue: any) => {
                        if(!inputsTypeValuePairs[attr.Id] && !this.inputsToSet.some(e => e.id === attr.Id) && attrValue.IsPreSelected && !this.tabsParsed) {
                            this.inputsToSet.push({id: attr.Id, value: attrValue.Id});
                        }

                        return { key: attrValue.Id, name: attrValue.Name,
                                text: attrValue.Locales && attrValue.Locales.length > 0 ? getLocaleName(attrValue.Locales, gnLanguage) : attrValue.Name, 
                                isSelected: inputsTypeValuePairs[attr.Id] ? inputsTypeValuePairs[attr.Id][attrValue.Id] : attrValue.IsPreSelected };
                    }) as IDropdownOption[];
                    break;
                case "DropdownList":
                case "MultiDropdownList":
                    control = controlType === 'MultiDropdownList' ? Controls.MultiPicker : Controls.Picker;
                    options.dropdown = attr.ProductAttributeValues.map((attrValue: any) => {
                        if(!inputsTypeValuePairs[attr.Id] && !this.inputsToSet.some(e => e.id === attr.Id) && attrValue.IsPreSelected && !this.tabsParsed) {
                            this.inputsToSet.push({id: attr.Id, value: attrValue.Id});
                        }
                        return { key: attrValue.Id, name: attrValue.Name,
                                text: attrValue.Locales && attrValue.Locales.length > 0 ? getLocaleName(attrValue.Locales, gnLanguage) : attrValue.Name, 
                                isSelected: attrValue.IsPreSelected };
                    }) as IDropdownOption[];
                    break;
                case "ColorSquares":
                case "Table":
                    control = Controls.TableInputs;

                    if(this.exampleDataLoadedFlag) {
                        skipRender = true;
                        this.exampleDataLoadedFlag = false;
                    }
                    break;
                case "CountrySearchList":
                    control = Controls.CountrySearchList;
                    break;
                case "SportSearchList":
                    control = Controls.SportSearchList;
                    break;
            }

            let attrName: string = "";
            let iconData: any = {};
            let validationData: any = {};
            let isInTab: boolean = false;
            let tabHasError: boolean = false;
            let saveAsTemplateForTable: string | boolean = false;
            let saveAsTemplateForCalculationAdjust: boolean | string = false;

            validationData = getInputValidationData(attribute);

            if(attribute.UserFields) {
                isInTab = isInputInTab(attribute.UserFields);
                saveAsTemplateForTable = shouldSaveAsTemplateForTable(attr.Id, attribute.UserFields, this.templateInputsForTable, productAttributes, this.props.allUserFields, product);
                if(!this.templateInputsForCalculationAdjust[attr.Id]) {
                    saveAsTemplateForCalculationAdjust = shouldSaveAsTemplateForCalculationAdjust(attr.Id, attribute.UserFields);
                }
            }

            if(attribute.Locales) {
                attrName = getLocaleName(attribute.Locales, gnLanguage);
            } else {
                attrName = attribute.Name;
            }

            if(controlType === "TextBox" || controlType === "ColorSquares" || controlType === "Table") {
                iconData = getInputIconData(attribute);
            }

            if(controlType === "DropdownList") {
                let dropdownOptions: IDropdownOption[] = [];
                if(isCountriesInput(attr)) {
                    dropdownOptions = getDropdownOptionsFromDataSource(attribute, this.props, "", this.props.gnLanguage);
                } else {
                    dropdownOptions = getDropdownOptionsFromDataSource(attribute, this.props, "Name", this.props.gnLanguage);
                }

                if(dropdownOptions && dropdownOptions.length > 0) {
                    options.dropdown = generateDropdownOptionsIfCountriesInput(attr, dropdownOptions);
                }
            }

            if(controlType === "ColorSquares" || controlType === "Table") {
                const tableOptions = getInputTableData(attribute, this.props.allUserFields, productAttributes, product);
                if(Object.keys(tableOptions).length > 0)
                    options.tableInputs = tableOptions;
            }

            if((attr.IsRequired || validationData.required) && (!inputsTypeValuePairs[attr.Id] || inputsTypeValuePairs[attr.Id].length === 0)) {
                tabHasError = true;
                this.tempInputErrors++;
            }

            const property = new ContentViewModelProperty(attr.Id, attrName, control, attr.IsRequired ? true : (validationData.required ? true : false), options, 
                                    (typeof this.props.asyncActionInProgress === 'boolean' && this.props.asyncActionInProgress === true) ? true : false, 
                                    { isDataLoaded: isDataLoaded, rows: (validationData.multiline ? validationData.multiline : textFieldRows),
                                    textType: (validationData.inputType ? validationData.inputType : "text"), validationData: validationData,
                                    
                                    additionalMethod: async (value: string, customPayload: any) => {
                                        this.props.changeAdjustInputsChangedManually(attr.Id, value);
                                    } }
                                );

            if(!this.savedAttributeOptions[attr.Id] && (options.dropdown.length > 0 || options.choicegroup.length > 0)) {
                this.savedAttributeOptions[attr.Id] = options;
            }

            if(saveAsTemplateForTable !== false && typeof saveAsTemplateForTable === 'string') {
                if(!this.templateInputsForTable[saveAsTemplateForTable]) {
                    this.templateInputsForTable[saveAsTemplateForTable] = [];
                }
                this.templateInputsForTable[saveAsTemplateForTable].push(property);

                if(!this.props.savedTemplateInputsForTable || 
                    JSON.stringify(this.templateInputsForTable) !== JSON.stringify(this.props.savedTemplateInputsForTable)
                ) {
                    this.props.setSavedTemplateInputsForTable(this.templateInputsForTable);
                }
            } else if(this.props.savedTemplateInputsForTable) {
                this.templateInputsForTable = this.props.savedTemplateInputsForTable;
            }
            if(saveAsTemplateForCalculationAdjust !== false && typeof saveAsTemplateForCalculationAdjust === 'string' && !this.templateInputsForCalculationAdjust[saveAsTemplateForCalculationAdjust]) {
                this.templateInputsForCalculationAdjust[saveAsTemplateForCalculationAdjust] = {};
                this.templateInputsForCalculationAdjust[saveAsTemplateForCalculationAdjust]['attr'] = attr;
                this.templateInputsForCalculationAdjust[saveAsTemplateForCalculationAdjust]['property'] = property;
                this.templateInputsForCalculationAdjust[saveAsTemplateForCalculationAdjust]['userFields'] = tempUserFields;

                this.props.saveInputsForCalculationAdjust(this.templateInputsForCalculationAdjust);
            }

            if(attr.DisplayOrder < 0 || skipRender) { // attr hidden
                if(skipRender) {
                    this.deleteFromTab(attr.ProductAttributeId);
                }
                return false;
            }

            this.tempInputIdUserFieldPairs[property.id] = tempUserFields;

            let conditionalAttributeResult: any = this.getMemoizedConditionalAttribute(
                attr, inputsTypeValuePairs, {...inputsIdUserFieldsPairs, [property.id]: tempUserFields}, product.ProductAttributeMappings, productAttributes, gnLanguage
            );
            
            let valuesAreTheSame: boolean = false;
            if(typeof inputsTypeValuePairs[attr.Id] !== 'undefined' && typeof this.conditionalDefaultValueAlreadySetFor[attr.Id] !== 'undefined') {
                if(typeof inputsTypeValuePairs[attr.Id] !== 'object') {
                    valuesAreTheSame = inputsTypeValuePairs[attr.Id] === this.conditionalDefaultValueAlreadySetFor[attr.Id].settedValue;
                } else {
                    valuesAreTheSame = JSON.stringify(inputsTypeValuePairs[attr.Id]) === JSON.stringify(this.conditionalDefaultValueAlreadySetFor[attr.Id].settedValue);
                }
            }

            const nowMinusLastSetDefaultTimestamp: number = now - this.lastSetDefaultValueTimestamp;
            if(typeof conditionalAttributeResult.newValue !== 'undefined' && typeof conditionalAttributeResult.newValue === 'string' && 
                (!this.conditionalDefaultValueAlreadySetFor[attr.Id] || 
                    this.conditionalDefaultValueAlreadySetFor[attr.Id].value !== conditionalAttributeResult.newValue || 
                    ((nowMinusLastSetDefaultTimestamp >= 0 && nowMinusLastSetDefaultTimestamp < 250) && (conditionalAttributeResult.newValue.length > 0 && !valuesAreTheSame))
                )
            ) {
                let valueToSet: string = conditionalAttributeResult.newValue;
                let inputsToSetUpdated: boolean = false;

                if(property.type === Controls.ChoiceGroup || 
                    property.type === Controls.CheckBoxOptions
                ) {
                    if(valueToSet.length === 0) {
                        let optionsToSet: any = {};
                        attr.ProductAttributeValues.forEach((attrValue: any) => {
                            optionsToSet[attrValue.Id] = false;
                        });

                        inputsToSetUpdated = true;
                        inputsToSet.push({id: attr.Id, value: optionsToSet, userFields: tempUserFields});
                    } else {
                        let splittedValue: string[] = valueToSet.split(', ');
                        let optionsToSet: any = {};

                        let optionsAlreadySetToTrue: string[] = [];
                        splittedValue.forEach((value: string) => {
                            attr.ProductAttributeValues.forEach((attrValue: any) => {
                                if(value === attrValue.Name) {
                                    optionsToSet[attrValue.Id] = true;
                                    optionsAlreadySetToTrue.push(attrValue.Id);
                                } else if(!optionsAlreadySetToTrue.includes(attrValue.Id)) {
                                    optionsToSet[attrValue.Id] = false;
                                }
                            });
                        });

                        if(Object.keys(optionsToSet).length > 0) {
                            valueToSet = optionsToSet;
                            inputsToSetUpdated = true;
                            inputsToSet.push({id: attr.Id, value: valueToSet, userFields: tempUserFields});
                        }
                    }
                } else if(property.type === Controls.Picker) {
                    const filteredAttrValue: any = filterBySome(attr.ProductAttributeValues, 'Name', valueToSet);   

                    if(filteredAttrValue && filteredAttrValue.Id && inputsTypeValuePairs[attr.Id] !== filteredAttrValue.Id &&
                        (!this.conditionalDefaultValueAlreadySetFor[attr.Id] || this.conditionalDefaultValueAlreadySetFor[attr.Id].settedValue !== filteredAttrValue.Id)
                    ) { 
                        valueToSet = filteredAttrValue.Id;
                        inputsToSetUpdated = true;
                        inputsToSet.push({id: attr.Id, value: valueToSet, userFields: tempUserFields});
                    }
                } else if(inputsTypeValuePairs[attr.Id] !== valueToSet) {
                    inputsToSetUpdated = true;
                    inputsToSet.push({id: attr.Id, value: valueToSet, userFields: tempUserFields});
                }

                this.lastSetDefaultValueTimestamp = + new Date();

                if(inputsToSetUpdated === true) {
                    this.conditionalDefaultValueAlreadySetFor[attr.Id] = {
                        value: conditionalAttributeResult.newValue,
                        settedValue: valueToSet
                    };

                    shouldMassUpdateInputs = true;
                }
            }

            if(conditionalAttributeResult.show === true) {
                if(tempUserFields === null || !isInTab) {
                    return renderElement(property, conditionalAttributeResult.show, iconData, this);
                } else {
                    this.parseTabs(tempUserFields, renderElement(property, conditionalAttributeResult.show, iconData, this), attr.Id, attr.ProductAttributeId, this.tabsParsed, tabHasError);
                    return false;
                }
            } else {
                if(this.tempInputErrors > this.prevTempInputErrors) {
                    this.tempInputErrors--;
                }

                if(!!tempUserFields) {
                    if(isInTab) {
                        this.deleteFromTab(attr.ProductAttributeId);
                    }
                    const isHideUserField: any = tempUserFields.filter((x: any) => x.Key === 'Hide');
                    if(!isHideUserField || !isHideUserField[0] || !isHideUserField[0].Value || isHideUserField[0].Value !== 'true') {
                        delete inputsTypeValuePairs[attr.Id];
                    }
                } else {
                    delete inputsTypeValuePairs[attr.Id];
                }
                delete this.conditionalDefaultValueAlreadySetFor[attr.Id];
                return false;
            }
        }) : []);

        this.savedAttributes = attributes;

        if(shouldMassUpdateInputs !== false) {
            this.setConditionalDefaultInputsData(inputsToSet);
        }

        return attributes;
    }

    private getPolicyCustomInputs() {
        if(this.insurersDropdownOptions.length === 0) {
            if(this.props.insurers && Array.isArray(this.props.insurers)) {
                this.props.insurers.forEach((insurer: InsurerDto, insurerIndex: number) => {
                    this.insurersDropdownOptions.push({ key: insurer.id, text: insurer.name });
                });
                this.forceUpdate();
            }
        }

        return <>
        <div>
            <LabeledTextField key={`policyNumber`} required={true} label={L('Policy number')} errorMessage={''} ref={this.tabPolicyNumberInput}
                defaultValue={this.props.inputsTypeValuePairs[`policyNumber`]}
                disabled={false} isDataLoaded={true}
                onChange={(e: any, newValue: string | undefined) => {
                    const elementId: string = `policyNumber`;
                    if(typeof this.debouncedOnInputChange === 'function') {
                        this.debouncedOnInputChange(elementId, newValue ? newValue : (e.target && e.target.value ? e.target.value : (typeof e === 'string' ? e : '')), this.tempInputIdUserFieldPairs && this.tempInputIdUserFieldPairs[elementId], undefined);
                    } else { 
                        this.props.onInputChange(elementId, newValue ? newValue : (e.target && e.target.value ? e.target.value : (typeof e === 'string' ? e : '')), this.tempInputIdUserFieldPairs && this.tempInputIdUserFieldPairs[elementId]);
                    }
                }} />

            <LabeledTextField key={`policyTotal`} required={true} label={L('Policy total')} errorMessage={''} ref={this.tabPolicyTotalInput}
                defaultValue={this.props.inputsTypeValuePairs[`policyTotal`]} type={'number'}
                disabled={false} isDataLoaded={true}
                onChange={(e: any, newValue: string | undefined) => {
                    const elementId: string = `policyTotal`;
                    if(typeof this.debouncedOnInputChange === 'function') {
                        this.debouncedOnInputChange(elementId, newValue ? newValue : (e.target && e.target.value ? e.target.value : (typeof e === 'string' ? e : '')), this.tempInputIdUserFieldPairs && this.tempInputIdUserFieldPairs[elementId], undefined);
                    } else { 
                        this.props.onInputChange(elementId, newValue ? newValue : (e.target && e.target.value ? e.target.value : (typeof e === 'string' ? e : '')), this.tempInputIdUserFieldPairs && this.tempInputIdUserFieldPairs[elementId]);
                    }
                }} />

            <DropdownBase key={`insurerId`} required={true} label={L('Insurer')} options={this.insurersDropdownOptions} value={this.props.inputsTypeValuePairs[`insurerId`]} ref={this.tabInsurerInput}
                disabled={false} isDataLoaded={true}
                onChange={(e: any) => {
                    const elementId: string = `insurerId`;
                    this.props.onInputChange(elementId, e, this.tempInputIdUserFieldPairs && this.tempInputIdUserFieldPairs[elementId]);
                }} />

            <DatePickerBase key={'endDate'} required={true} label={L('End date')} ref={this.tabEndDateInput}
                value={!!this.props.inputsTypeValuePairs[`endDate`] ? moment(this.props.inputsTypeValuePairs[`endDate`]) : this.props.inputsTypeValuePairs[`endDate`]}
                disabled={false} isDataLoaded={true}
                onChange={(value: string | undefined) => {
                    const elementId: string = `endDate`;
                    this.props.onInputChange(elementId, moment(value).utc().format('YYYY-MM-DDTHH:mm:ss[Z]'), this.tempInputIdUserFieldPairs && this.tempInputIdUserFieldPairs[elementId]);
                }} />

            <DatePickerBase key={'policyDate'} required={true} label={L('Policy date')} ref={this.tabPolicyDateInput}
                value={!!this.props.inputsTypeValuePairs[`policyDate`] ? moment(this.props.inputsTypeValuePairs[`policyDate`]) : this.props.inputsTypeValuePairs[`policyDate`]} 
                disabled={false} isDataLoaded={true}
                onChange={(value: string | undefined) => {
                    const elementId: string = `policyDate`;
                    this.props.onInputChange(elementId, moment(value).utc().format('YYYY-MM-DDTHH:mm:ss[Z]'), this.tempInputIdUserFieldPairs && this.tempInputIdUserFieldPairs[elementId]);
                }} />
                
            {/* <DropdownBase key={`policyStatus`} required={true} label={L('Policy status')} options={enumToDropdownOptions(InsurancePolicyStatus, false, true, "string")} 
                    value={this.props.inputsTypeValuePairs[`policyStatus`]} disabled={false} isDataLoaded={true}
                    onChange={(e: any) => {
                        const elementId: string = `policyStatus`;
                        this.props.onInputChange(elementId, e, this.tempInputIdUserFieldPairs && this.tempInputIdUserFieldPairs[elementId]);
                    }} /> */}
            </div>
        </>;
    }

    handleTabSwitch = (tabKey?: string) => {
        const tempCurrentSelectedKey: number = parseInt(this.tabsSelectedKey);
        this.tabsSelectedKey = (typeof tabKey === 'string' && !!tabKey) ? tabKey : (tempCurrentSelectedKey === (this.tabs.length - 1) ? '0' : (tempCurrentSelectedKey + 1).toString());

        if (this.props.onTabChange) {
            this.props.onTabChange(this.tabsSelectedKey);
        }
        this.forceUpdate();
    }

    render() {
        const {productAttributes, product, inputValuePairsStringified, customInputsData} = this.props;

        if(product && product.UserFields && this.tabs.length === 0 && !this.tabsParsed) {
            this.setTabs(product.UserFields);
        }
        
        let attributes: any;
        if(Object.keys(this.savedAttributes).length === 0 || this.prevInputValuePairsStringified !== inputValuePairsStringified || 
            this.prevAsyncActionInprogress !== this.props.asyncActionInProgress
        ) {
            this.tabs.forEach((tab: any, tabIndex: number) => {
                this.tabs[tabIndex].tabErrorsCount = 0;
            });

            attributes = this.mapAttributes();
        } else {
            attributes = this.savedAttributes;
        }

        this.prevInputValuePairsStringified = inputValuePairsStringified;
        this.prevAsyncActionInprogress = this.props.asyncActionInProgress;

        attributes = attributes.filter((x: any) => x !== false);

        if(productAttributes && !this.tabsParsed) {
            this.tabsParsed = true;
        }
        
        if(this.props.inputErrors !== this.tempInputErrors) {
            this.props.setInputErrors(this.tempInputErrors);
        }

        const tabNamesToHide: string[] = ['Opcje zaawansowane', 'Advanced options'];
        const tabNamesToAppend: string[] = ['Dane dotyczące polisy', 'Policy data'];
        let inputsToMassChange: any[] = [];
        let tabs = this.tabs.map((tab: any, tabIndex: number) => {
            if(tab.key === `step${AppConfig.policyFormInsurerDataTabIndex + 1}`) {
                return <></>;
            }
            
            tab.items = tab.items.slice(0).sort(this.sortByOrder);

            let tabItems: any[] = [];
            if(tabNamesToAppend.includes(tab.name)) {
                tabItems.push(this.getPolicyCustomInputs());
            }

            tab.items.forEach((item: any) => {
                if(!item.hide) {
                    tabItems.push(<div className={classNames.myTabItem} key={item.key}>{item.value}</div>);
                } else if(this.props.isEditMode && this.tabInputsInEditModeSet === false) {
                    inputsToMassChange.push(item);
                }
            });

            if(tabNamesToHide.includes(tab.name)) {
                return <></>;
            } else if(tab.name === 'APK') {
                return <PivotItem headerText={tab.name} key={tab.key + tab.items.length}>
                    <FocusZone direction={FocusZoneDirection.horizontal} handleTabKey={FocusZoneTabbableElements.all} isCircularNavigation={true}>
                        <DefaultButton theme={myTheme} style={{marginTop: 15}} onClick={() => { this.isApkListModalOpen = true; this.forceUpdate(); }} text={L('Use saved APK')} 
                            iconProps={{ iconName: 'none' }} disabled={this.props.asyncActionInProgress} />
                        {tabItems}
                    </FocusZone>
                </PivotItem>;
            } else {
                return <PivotItem headerText={tab.name} key={tabIndex.toString()} itemKey={tabIndex.toString()} itemIcon={tab.tabErrorsCount && tab.tabErrorsCount > 0 ? 'Error' : undefined}
                            itemCount={tab.tabErrorsCount && tab.tabErrorsCount > 0 ? tab.tabErrorsCount : undefined} className={classNames.pivotItemsContainer}
                        >
                    <FocusZone direction={FocusZoneDirection.horizontal} handleTabKey={FocusZoneTabbableElements.all} isCircularNavigation={true}>
                        { (product && product.SeName === 'ubezpieczenie-auta' && tabIndex === AppConfig.tabNumberForCustomInputsInMotorInsurance - 1) && 
                            <EurotaxExpertInfoMixedCustomInputsBox showCustomInputsBox={this.showEurotaxExpertInfoMixedCustomInputsBox} customInputsData={customInputsData}
                                onInputChange={ (id: string, value: any) => this.props.onInputChange(id, value, this.tempInputIdUserFieldPairs[id]) }
                                mapKeyToId={(mapType: string, key: string) => this.props.mapKeyToId(mapType, key)}
                                onToggleClose={() => { this.showEurotaxExpertInfoMixedCustomInputsBox = false; this.forceUpdate(); }} 
                                setEurotaxInfoexpertFormData={ (id: string, value: string) => this.props.setEurotaxInfoexpertFormData(id, value) }
                                toggleAsyncActionFlag={this.props.toggleAsyncActionFlag}
                            /> }    
                        {tabItems}
                    </FocusZone>
                </PivotItem>;
            }
        });

        if(this.tabInputsInEditModeSet === false) {
            this.tabInputsInEditModeSet = true;
            
            const cloneInputsTypeValuePairs = {};
            const cloneInputsIdUserFieldsPairs = {};

            inputsToMassChange.forEach((element) => {
                if(!!element.value.props.value) {
                    cloneInputsTypeValuePairs[element.value.key] = !!element.value.props.value ? 
                                                            (isJsonString(element.value.props.value) ? 
                                                                JSON.parse(element.value.props.value) : element.value.props.value) 
                                                            : '';
                    cloneInputsIdUserFieldsPairs[element.value.key] = this.tempInputIdUserFieldPairs[element.value.key];
                }
            });

            this.props.onMassInputChange(cloneInputsTypeValuePairs, cloneInputsIdUserFieldsPairs);
        }

        this.lastRenderTimestamp = + new Date();

        const pivotStyles = {
            root: {
                marginLeft: '-8px'
            },
            linkIsSelected: {
                color: myTheme.palette.red,
                selectors: {
                    ':before': {
                        height: '5px',
                        backgroundColor: additionalTheme.darkerRed,
                    }
                }
            }
        };

        return <>
            <Modal
                titleAriaId={'selectVehicleModal'}
                isOpen={this.isVehicleListModalOpen}
                onDismiss={() => { this.isVehicleListModalOpen = false; this.forceUpdate(); }}
                isBlocking={true}
                containerClassName={classNames.container}
            >
                <div className={classNames.header}>
                    {/* <span id={'selectCustomerModal'}>{L("Select customer:")}</span> */}
                    <IconButton
                        styles={iconButtonStyles}
                        iconProps={cancelIcon}
                        ariaLabel={L("Close popup modal")}
                        onClick={() => { this.isVehicleListModalOpen = false; this.props.onTempVehicleSelect(undefined, false); }}
                    />
                </div>

                <div className={classNames.contentContainer}>
                    <VehicleListBase selectedVehicle={this.props.tempSelectedVehicle} vehicleStore={this.props.vehicleStore} alignItemsToRight={true}
                        onVehicleSelect={ (tempVehicle: any) => {
                            this.props.onTempVehicleSelect(tempVehicle, false); 
                        }}
                        showCounter={false} showFilters={false} showFillDataButton={true}
                        onFillDataButtonClick={(selectedVehicle: any) => {
                            let getIdFromInputKeys: any = {
                                "vehicleType": this.props.mapKeyToId("mapAttributeNameToId", "vehicleType"),
                                "productionYear": this.props.mapKeyToId("mapAttributeNameToId", "productionYear"),
                                "vin": this.props.mapKeyToId("mapAttributeNameToId", "vin"),
                                "registrationNumber": this.props.mapKeyToId("mapAttributeNameToId", "registrationNumber"),
                                "firstRegistrationDate": this.props.mapKeyToId("mapAttributeNameToId", "firstRegistrationDate"),
                                "fuelType": this.props.mapKeyToId("mapAttributeNameToId", "fuelType"),
                                "eurotaxId": this.props.mapKeyToId("mapAttributeNameToId", "eurotaxId"),
                                "expertInfoId": this.props.mapKeyToId("mapAttributeNameToId", "expertInfoId"),
                                "findAVehicle": this.props.mapKeyToId("mapAttributeNameToId", "findAVehicle"),
                                "mileage": this.props.mapKeyToId("mapAttributeNameToId", "mileage"),
                            };

                            let eurotaxSet: boolean = selectedVehicle.eurotaxCarId && selectedVehicle.eurotaxCarId.length > 0;
                            let infoExpertSet: boolean = selectedVehicle.infoExpertId && selectedVehicle.infoExpertId.length > 0;

                            const cloneInputsTypeValuePairs = {
                                [getIdFromInputKeys["productionYear"]]: selectedVehicle.productionYear,
                                [getIdFromInputKeys["vin"]]: selectedVehicle.vin,
                                [getIdFromInputKeys["registrationNumber"]]: selectedVehicle.registrationNumber,
                                [getIdFromInputKeys["firstRegistrationDate"]]: selectedVehicle.firstRegistrationDate,
                                [getIdFromInputKeys["fuelType"]]: selectedVehicle.fuelType,
                                [getIdFromInputKeys["eurotaxId"]]: selectedVehicle.eurotaxCarId && selectedVehicle.eurotaxCarId.toString(),
                                [getIdFromInputKeys["expertInfoId"]]: selectedVehicle.infoExpertId && selectedVehicle.infoExpertId.toString(),
                                [getIdFromInputKeys["mileage"]]: selectedVehicle.mileage,
                                [getIdFromInputKeys["vehicleType"]]: selectedVehicle.vehicleType,
                                [getIdFromInputKeys["findAVehicle"]]: (eurotaxSet && infoExpertSet) ?
                                                                            L("Eurotax ID and Expert's information ID is now set.") :
                                                                                (eurotaxSet ? L('Success! Eurotax ID is now set.') :
                                                                                    (infoExpertSet ? L("Success! Expert's information ID is now set.") : ""))
                            };
                            const cloneInputsIdUserFieldsPairs = {
                                [getIdFromInputKeys["productionYear"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["productionYear"]],
                                [getIdFromInputKeys["vin"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["vin"]],
                                [getIdFromInputKeys["registrationNumber"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["registrationNumber"]],
                                [getIdFromInputKeys["firstRegistrationDate"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["firstRegistrationDate"]],
                                [getIdFromInputKeys["fuelType"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["fuelType"]],
                                [getIdFromInputKeys["eurotaxId"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["eurotaxId"]],
                                [getIdFromInputKeys["expertInfoId"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["expertInfoId"]],
                                [getIdFromInputKeys["mileage"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["mileage"]],
                                [getIdFromInputKeys["vehicleType"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["vehicleType"]],
                                [getIdFromInputKeys["findAVehicle"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["findAVehicle"]],
                            };

                            this.isVehicleListModalOpen = false;
                            this.props.onMassInputChange(cloneInputsTypeValuePairs, cloneInputsIdUserFieldsPairs);
                        }}
                    />
                </div>
            </Modal>

            <Modal
                titleAriaId={'selectVehicleModal'}
                isOpen={this.isApkListModalOpen}
                onDismiss={() => { this.isApkListModalOpen = false; this.forceUpdate(); }}
                isBlocking={true}
                containerClassName={classNames.container}
            >
                <div className={classNames.header}>
                    {/* <span id={'selectCustomerModal'}>{L("Select customer:")}</span> */}
                    <IconButton
                        styles={iconButtonStyles}
                        iconProps={cancelIcon}
                        ariaLabel={L("Close popup modal")}
                        onClick={() => { this.isApkListModalOpen = false; this.props.onTempApkSelect(undefined, false); }}
                    />
                </div>

                <div className={classNames.contentContainer}>
                    <ApkListBase selectedApk={this.props.tempSelectedApk ? this.props.tempSelectedApk.id.toString() : ''} 
                        apkAttachedFilesStore={this.props.apkAttachedFilesStore} alignItemsToRight={true}
                        onApkSelect={ (tempApk: any) => {
                            this.props.onTempApkSelect(tempApk, false); 
                        }}
                        asyncActionInProgress={this.props.asyncActionInProgress}
                        showCounter={false} showFilters={false} showFillDataButton={true}
                        toggleAsyncActionFlag={(newState: boolean, forceUpdate: boolean) => this.props.toggleAsyncActionFlag(newState, forceUpdate) }
                        onFillDataButtonClick={(selectedApk: any) => {
                            this.props.toggleAsyncActionFlag(true, false);

                            let parsedApkPayload = selectedApk && !!selectedApk.payload && isJsonString(selectedApk.payload) ? JSON.parse(selectedApk.payload) : [];
                            let getIdFromInputKeys: any = {
                                "ApkOC": this.props.mapKeyToId("mapAttributeNameToId", "ApkOC"),
                                "ApkAC": this.props.mapKeyToId("mapAttributeNameToId", "ApkAC"),
                                "ApkYoungerAnotherDriver": this.props.mapKeyToId("mapAttributeNameToId", "ApkYoungerAnotherDriver"),
                                "ApkRightHandDrive": this.props.mapKeyToId("mapAttributeNameToId", "ApkRightHandDrive"),
                                "ApkHaveCoOwner": this.props.mapKeyToId("mapAttributeNameToId", "ApkHaveCoOwner"),
                                "ApkOwnContributionAc": this.props.mapKeyToId("mapAttributeNameToId", "ApkOwnContributionAc"),
                                "ApkUsageType": this.props.mapKeyToId("mapAttributeNameToId", "ApkUsageType"),
                                "ApkAdditionalProtection": this.props.mapKeyToId("mapAttributeNameToId", "ApkAdditionalProtection"),
                                "ApkScopeASS": this.props.mapKeyToId("mapAttributeNameToId", "ApkScopeASS"),
                                "ApkScopeAC": this.props.mapKeyToId("mapAttributeNameToId", "ApkScopeAC"),
                            };

                            let optionsToIdPairs: any = {};

                            for(let inputKey in getIdFromInputKeys) {
                                if(getIdFromInputKeys.hasOwnProperty(inputKey)) {
                                    const foundItem: any = this.savedAttributeOptions[getIdFromInputKeys[inputKey]].dropdown.length > 0 ? 
                                                            this.savedAttributeOptions[getIdFromInputKeys[inputKey]].dropdown : 
                                                                this.savedAttributeOptions[getIdFromInputKeys[inputKey]].choicegroup;
                                    if(foundItem && foundItem.length > 0) {
                                        optionsToIdPairs[getIdFromInputKeys[inputKey]] = foundItem;
                                    }
                                }
                            }

                            let selectedCheckboxInputs: any = {
                                [getIdFromInputKeys["ApkAdditionalProtection"]]: {},
                                [getIdFromInputKeys["ApkScopeASS"]]: {},
                            };

                            const filteredAdditionalProtection: any = parsedApkPayload.filter((x: any) => x.Key === 'Auto.APK.AdditionalProtection');
                            const filteredScopeASS: any = parsedApkPayload.filter((x: any) => x.Key === 'Auto.APK.ScopeASS');
                            let tempSplittedCheckboxInputsData: any = {
                                [getIdFromInputKeys["ApkAdditionalProtection"]]: filteredAdditionalProtection && filteredAdditionalProtection[0] && !!filteredAdditionalProtection[0].Answer ? filteredAdditionalProtection[0].Answer.split(', ') : [],
                                [getIdFromInputKeys["ApkScopeASS"]]: filteredScopeASS && filteredScopeASS[0] && !!filteredScopeASS[0].Answer ? filteredScopeASS[0].Answer.split(', ') : [],
                            };

                            for(let inputId in tempSplittedCheckboxInputsData) {
                                if(tempSplittedCheckboxInputsData.hasOwnProperty(inputId)) {
                                    optionsToIdPairs[inputId].forEach((option: any) => {
                                        tempSplittedCheckboxInputsData[inputId].some((apkOption: any) => {
                                            if(apkOption === option.text) {
                                                selectedCheckboxInputs[inputId] = {
                                                    ...selectedCheckboxInputs[inputId],
                                                    [option.key]: true,
                                                };
                                                return true;
                                            }
                                            return false;
                                        });
                                    });
                                }
                            }

                            const filteredOptions = {
                                [getIdFromInputKeys["ApkOC"]]: optionsToIdPairs[getIdFromInputKeys["ApkOC"]] && optionsToIdPairs[getIdFromInputKeys["ApkOC"]].filter((option: any) => option.text === parsedApkPayload.filter((x: any) => x.Key === 'Auto.APK.OC')[0].Answer).length > 0 ? 
                                    optionsToIdPairs[getIdFromInputKeys["ApkOC"]].filter((option: any) => option.text === parsedApkPayload.filter((x: any) => x.Key === 'Auto.APK.OC')[0].Answer) : [],
                                [getIdFromInputKeys["ApkAC"]]: optionsToIdPairs[getIdFromInputKeys["ApkAC"]] && optionsToIdPairs[getIdFromInputKeys["ApkAC"]].filter((option: any) => option.text === parsedApkPayload.filter((x: any) => x.Key === 'Auto.APK.AC')[0].Answer).length > 0 ?  
                                    optionsToIdPairs[getIdFromInputKeys["ApkAC"]].filter((option: any) => option.text === parsedApkPayload.filter((x: any) => x.Key === 'Auto.APK.AC')[0].Answer) : [],
                                [getIdFromInputKeys["ApkYoungerAnotherDriver"]]: optionsToIdPairs[getIdFromInputKeys["ApkYoungerAnotherDriver"]] && optionsToIdPairs[getIdFromInputKeys["ApkYoungerAnotherDriver"]].filter((option: any) => option.text === parsedApkPayload.filter((x: any) => x.Key === 'Auto.APK.YoungerAnotherDriver')[0].Answer).length > 0 ?  
                                    optionsToIdPairs[getIdFromInputKeys["ApkYoungerAnotherDriver"]].filter((option: any) => option.text === parsedApkPayload.filter((x: any) => x.Key === 'Auto.APK.YoungerAnotherDriver')[0].Answer) : [],
                                [getIdFromInputKeys["ApkRightHandDrive"]]: optionsToIdPairs[getIdFromInputKeys["ApkRightHandDrive"]] && optionsToIdPairs[getIdFromInputKeys["ApkRightHandDrive"]].filter((option: any) => option.text === parsedApkPayload.filter((x: any) => x.Key === 'Auto.APK.RightHandDrive')[0].Answer).length > 0 ?  
                                    optionsToIdPairs[getIdFromInputKeys["ApkRightHandDrive"]].filter((option: any) => option.text === parsedApkPayload.filter((x: any) => x.Key === 'Auto.APK.RightHandDrive')[0].Answer) : [],
                                [getIdFromInputKeys["ApkHaveCoOwner"]]: optionsToIdPairs[getIdFromInputKeys["ApkHaveCoOwner"]] && optionsToIdPairs[getIdFromInputKeys["ApkHaveCoOwner"]].filter((option: any) => option.text === parsedApkPayload.filter((x: any) => x.Key === 'Auto.APK.HaveCoOwner')[0].Answer).length > 0 ?  
                                    optionsToIdPairs[getIdFromInputKeys["ApkHaveCoOwner"]].filter((option: any) => option.text === parsedApkPayload.filter((x: any) => x.Key === 'Auto.APK.HaveCoOwner')[0].Answer) : [],
                                [getIdFromInputKeys["ApkOwnContributionAc"]]: optionsToIdPairs[getIdFromInputKeys["ApkOwnContributionAc"]] && optionsToIdPairs[getIdFromInputKeys["ApkOwnContributionAc"]].filter((option: any) => option.text === parsedApkPayload.filter((x: any) => x.Key === 'Auto.APK.OwnContributionAc')[0].Answer).length > 0 ?  
                                    optionsToIdPairs[getIdFromInputKeys["ApkOwnContributionAc"]].filter((option: any) => option.text === parsedApkPayload.filter((x: any) => x.Key === 'Auto.APK.OwnContributionAc')[0].Answer) : [],
                                [getIdFromInputKeys["ApkUsageType"]]: optionsToIdPairs[getIdFromInputKeys["ApkUsageType"]] && optionsToIdPairs[getIdFromInputKeys["ApkUsageType"]].filter((option: any) => option.text === parsedApkPayload.filter((x: any) => x.Key === 'Auto.APK.UsageType')[0].Answer).length > 0 ?  
                                    optionsToIdPairs[getIdFromInputKeys["ApkUsageType"]].filter((option: any) => option.text === parsedApkPayload.filter((x: any) => x.Key === 'Auto.APK.UsageType')[0].Answer) : [],
                                [getIdFromInputKeys["ApkScopeAC"]]: optionsToIdPairs[getIdFromInputKeys["ApkScopeAC"]] && optionsToIdPairs[getIdFromInputKeys["ApkScopeAC"]].filter((option: any) => option.text === parsedApkPayload.filter((x: any) => x.Key === 'Auto.APK.ScopeAC')[0].Answer).length > 0 ?  
                                    optionsToIdPairs[getIdFromInputKeys["ApkScopeAC"]].filter((option: any) => option.text === parsedApkPayload.filter((x: any) => x.Key === 'Auto.APK.ScopeAC')[0].Answer) : [],
                            };

                            const cloneInputsTypeValuePairs = {
                                [getIdFromInputKeys["ApkOC"]]: filteredOptions[getIdFromInputKeys["ApkOC"]][0] ? filteredOptions[getIdFromInputKeys["ApkOC"]][0].key : this.props.inputsTypeValuePairs[getIdFromInputKeys["ApkOC"]],
                                [getIdFromInputKeys["ApkAC"]]: filteredOptions[getIdFromInputKeys["ApkAC"]][0] ? filteredOptions[getIdFromInputKeys["ApkAC"]][0].key : this.props.inputsTypeValuePairs[getIdFromInputKeys["ApkAC"]],
                                [getIdFromInputKeys["ApkYoungerAnotherDriver"]]: filteredOptions[getIdFromInputKeys["ApkYoungerAnotherDriver"]][0] ? filteredOptions[getIdFromInputKeys["ApkYoungerAnotherDriver"]][0].key : this.props.inputsTypeValuePairs[getIdFromInputKeys["ApkYoungerAnotherDriver"]],
                                [getIdFromInputKeys["ApkRightHandDrive"]]: filteredOptions[getIdFromInputKeys["ApkRightHandDrive"]][0] ? filteredOptions[getIdFromInputKeys["ApkRightHandDrive"]][0].key : this.props.inputsTypeValuePairs[getIdFromInputKeys["ApkRightHandDrive"]],
                                [getIdFromInputKeys["ApkHaveCoOwner"]]: filteredOptions[getIdFromInputKeys["ApkHaveCoOwner"]][0] ? filteredOptions[getIdFromInputKeys["ApkHaveCoOwner"]][0].key : this.props.inputsTypeValuePairs[getIdFromInputKeys["ApkHaveCoOwner"]],
                                [getIdFromInputKeys["ApkOwnContributionAc"]]: filteredOptions[getIdFromInputKeys["ApkOwnContributionAc"]][0] ? filteredOptions[getIdFromInputKeys["ApkOwnContributionAc"]][0].key : this.props.inputsTypeValuePairs[getIdFromInputKeys["ApkOwnContributionAc"]],
                                [getIdFromInputKeys["ApkUsageType"]]: filteredOptions[getIdFromInputKeys["ApkUsageType"]][0] ? filteredOptions[getIdFromInputKeys["ApkUsageType"]][0].key : this.props.inputsTypeValuePairs[getIdFromInputKeys["ApkUsageType"]],
                                [getIdFromInputKeys["ApkScopeAC"]]: filteredOptions[getIdFromInputKeys["ApkScopeAC"]][0] ? filteredOptions[getIdFromInputKeys["ApkScopeAC"]][0].key : this.props.inputsTypeValuePairs[getIdFromInputKeys["ApkScopeAC"]],
                                [getIdFromInputKeys["ApkAdditionalProtection"]]: Object.keys(selectedCheckboxInputs[getIdFromInputKeys["ApkAdditionalProtection"]]).length > 0 ? selectedCheckboxInputs[getIdFromInputKeys["ApkAdditionalProtection"]] : this.props.inputsTypeValuePairs[getIdFromInputKeys["ApkAdditionalProtection"]],
                                [getIdFromInputKeys["ApkScopeASS"]]: Object.keys(selectedCheckboxInputs[getIdFromInputKeys["ApkScopeASS"]]).length > 0 ? selectedCheckboxInputs[getIdFromInputKeys["ApkScopeASS"]] : this.props.inputsTypeValuePairs[getIdFromInputKeys["ApkScopeASS"]],
                            };

                            const cloneInputsIdUserFieldsPairs = {
                                [getIdFromInputKeys["ApkOC"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["ApkOC"]],
                                [getIdFromInputKeys["ApkAC"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["ApkAC"]],
                                [getIdFromInputKeys["ApkYoungerAnotherDriver"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["ApkYoungerAnotherDriver"]],
                                [getIdFromInputKeys["ApkRightHandDrive"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["ApkRightHandDrive"]],
                                [getIdFromInputKeys["ApkHaveCoOwner"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["ApkHaveCoOwner"]],
                                [getIdFromInputKeys["ApkOwnContributionAc"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["ApkOwnContributionAc"]],
                                [getIdFromInputKeys["ApkUsageType"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["ApkUsageType"]],
                                [getIdFromInputKeys["ApkScopeAC"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["ApkScopeAC"]],
                                [getIdFromInputKeys["ApkAdditionalProtection"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["ApkAdditionalProtection"]],
                                [getIdFromInputKeys["ApkScopeASS"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["ApkScopeASS"]],
                            };

                            this.isApkListModalOpen = false;
                            this.props.toggleAsyncActionFlag(false, false);
                            this.props.onMassInputChange(cloneInputsTypeValuePairs, cloneInputsIdUserFieldsPairs);
                        }}
                    />
                </div>
            </Modal>

            {/* <div style={{display: 'flex', flexDirection: 'row', 'alignItems': 'center'}}>
                {!isConfigForProduction() &&
                    <DefaultButton text={L('Fill with example data')} iconProps={{ iconName: 'TestPlan' }} allowDisabledFocus style={{margin: '15px 25px 15px 0', display: 'none'}} disabled={this.fillingExampleDataActionInProgress} 
                        onClick={() => { this.fillWithExampleData(); }}
                    />
                }

                {this.fillingExampleDataActionInProgress ? <Spinner label={L('Filling in data...')} className={classNames.loadSpinnerTopBar} size={SpinnerSize.medium} ariaLive="assertive" labelPosition="right" /> : ''}
            </div> */}

            <Pivot className={`${this.tabs.length > 0 ? '' : classNames.toolbar}`} theme={myTheme} styles={pivotStyles} selectedKey={this.tabsSelectedKey}
                onLinkClick={(item?: PivotItem) => {
                    if (item && item.props) {
                        this.handleTabSwitch(item.props.itemKey);
                        this.forceUpdate();
                    }
                }
            }>
                {this.tabs.length > 0 ?
                    tabs :
                    <PivotItem>
                        {attributes}
                    </PivotItem>
                }
            </Pivot>
        </>;
    }

    private getMemoizedConditionalAttribute(attr: any, inputsTypeValuePairs: any, idUserFieldsPairs: any, mappings: any, items: any, language: any): any {
        const attrId: string = attr.Id;
        
        // Determine which inputs affect this attribute
        if (!this.inputDependencies.has(attrId)) {
            const analyzeResult: {dependenciesIds: Set<string>, tempKeyIdPairs: Map<string, string>} = analyzeAttributeDependencies(
                attrId, attr, this.keyIdPairs, this.tempInputIdUserFieldPairs[attrId], mappings, this.props.product
            );

            this.inputDependencies.set(attrId, analyzeResult.dependenciesIds);
            this.keyIdPairs = new Map(analyzeResult.tempKeyIdPairs);
        }
        
        // Get the relevant inputs for this attribute
        const relevantInputs = this.inputDependencies.get(attrId) || new Set(Object.keys(inputsTypeValuePairs));
        
        // Create a dependency key from only the relevant inputs
        const inputsStateKey: string = createInputsStateKey(Array.from(relevantInputs), inputsTypeValuePairs);
        
        // Check cache
        const cacheKey: string = `${attrId}_${inputsStateKey}`;
        if (this.conditionalAttributeCache.has(cacheKey)) {
            return this.conditionalAttributeCache.get(cacheKey);
        }
        
        // Calculate new result
        const result = conditionalAttribute(attr, inputsTypeValuePairs, idUserFieldsPairs, mappings, items, language);
        
        // Cache the result
        this.conditionalAttributeCache.set(cacheKey, result);
        
        return result;
    }
}