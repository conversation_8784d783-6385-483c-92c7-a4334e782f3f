import { ClientDto } from "../client/dto/clientDto";
import { BaseApiEntityModel } from "../dto/BaseApiEntityModel";

export interface VehicleConfigDto extends BaseApiEntityModel {
    vin: string,
    firstRegistrationDate: string,
    registrationNumber: string,
    mileage: number,
    type: string,
    brand: string,
    year: string,
    fuelType: string,
    engineCapacity: string,
    vehicleModel: string,
    enginePower: string,
    eurotaxCarId: string,
    eurotaxCarName: string,
    infoExpertId: string,
    infoExpertName: string,
    vehicleInfo: string,
    lastModificationTime: string,
    lastModifierUserId: number,
    creationTime: string,
    creatorUserId: number,
    dmc: number;
    netWeight: number | null;
    capacity: number;
    ownerId: number,
    owner: ClientDto,
    coOwnerId: number,
    coOwner: ClientDto,
    vehicleUserId: number,
    vehicleUser: ClientDto,
    policyHolderId: number,
    policyHolder: ClientDto,
}