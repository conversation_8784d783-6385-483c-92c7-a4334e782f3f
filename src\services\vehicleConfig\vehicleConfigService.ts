import { isUserLoggedIn } from "../../utils/authUtils";
import { getPartialModel } from "../../utils/modelUtils";
import { CrudServiceBase } from "../base/crudServiceBase";
import { PagedResultDto } from "../dto/pagedResultDto";
import Endpoint from "../endpoint";
import { httpApi } from "../httpService";
import { VehicleConfigDto } from "./vehicleConfigDto";

export class VehicleConfigService extends CrudServiceBase<VehicleConfigDto> {
    constructor() {
        super(Endpoint.VehicleConfig);
        this.internalHttp = httpApi;
    }

    public async create(createInput: VehicleConfigDto) {
        isUserLoggedIn();

        const filteredModel = getPartialModel(createInput, [], ['policyHolder', 'owner', 'coOwner', 'vehicleUser', 'creationTime', 'creatorUserId', 'lastModificationTime', 'lastModifierUserId']);
        filteredModel.mileage = parseInt(filteredModel.mileage);

        let result = await this.internalHttp.post(this.endpoint.Create(), filteredModel);
        return result.hasOwnProperty("data") && result.data.hasOwnProperty("result") ? result.data.result : result.data;
    }

    public async update(updateInput: VehicleConfigDto) {
        isUserLoggedIn();
        
        const filteredModel = getPartialModel(updateInput, [], ['policyHolder', 'owner', 'coOwner', 'vehicleUser', 'creationTime', 'creatorUserId', 'lastModificationTime', 'lastModifierUserId']);
        filteredModel.mileage = parseInt(filteredModel.mileage);

        let result = await this.internalHttp.put(this.endpoint.Update(), filteredModel);
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async saveByVin(createInput: VehicleConfigDto) {
        isUserLoggedIn();

        const filteredModel = getPartialModel(createInput, [], ['policyHolder', 'owner', 'coOwner', 'vehicleUser', 'creationTime', 'creatorUserId', 'lastModificationTime', 'lastModifierUserId']);
        filteredModel.mileage = parseInt(filteredModel.mileage);

        let result = await this.internalHttp.post(this.endpoint.Custom(`SaveByVin`), filteredModel);
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getByVin(vin: string): Promise<PagedResultDto<VehicleConfigDto>> {
        isUserLoggedIn();
        let result = await this.internalHttp.get(this.endpoint.Custom(`GetByVin?vin=${vin}`));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getByRegistrationNumber(registrationNumber: string): Promise<PagedResultDto<VehicleConfigDto>> {
        isUserLoggedIn();
        let result = await this.internalHttp.get(this.endpoint.Custom(`GetByRegistrationNumber?registrationNumber=${registrationNumber}`));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getByClientId(clientId: string): Promise<PagedResultDto<VehicleConfigDto>> {
        isUserLoggedIn();
        let result = await this.internalHttp.get(this.endpoint.Custom(`GetByOwnerId?ownerId=${clientId}`));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }
}

const exportVehicleConfigService: VehicleConfigService = new VehicleConfigService();
export default exportVehicleConfigService;