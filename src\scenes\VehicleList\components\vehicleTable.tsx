import { ITableColumn } from "../../BaseComponents/ITableColumn";
import { L } from '../../../lib/abpUtility';
import { FluentTableBase } from '../../Fluent/base/fluentTableBase';
import { IGenericPanelProps } from '../../Fluent/base/genericPanel';
import { ICrudPermissons } from "../../BaseComponents/commandBarBase";
import { RouterPath } from "../../../components/Router/router.config";
import { Link } from "@fluentui/react";
import { VehicleDto } from "../../../services/vehicle/vehicleDto";
import { VehiclePanel } from "./vehiclePanel";

export class VehicleTable extends FluentTableBase<VehicleDto> {
  getItemDisplayNameOf(item: VehicleDto): string {
    return `${item && !!item.registrationNumber ? item.registrationNumber : ''}`;
  }

  getColumns(): ITableColumn[] {
    return VehicleTable.getTableColumns(this.props);
  }

  static getTableColumns(props: any): ITableColumn[] {
    return [
      {
        name: L('ID'),
        fieldName: 'id',
        minWidth: 30,
        maxWidth: 30,
        onRender: (item: any): any => {
          return <Link onClick={(e) => {
                        e.preventDefault();
                        props.history.push(`/${RouterPath.Vehicle}/${item.id}`);
                      }} 
                        href={`/${RouterPath.Vehicle}/${item.id}`}>
                  {item.id}
                </Link>
        }
      },
      {
        name: L('insurer'),
        fieldName: 'policyHolder.user.fullName',
        minWidth: 170,
        maxWidth: 170,
        onRender: (item: any): any => {
          if(item && item.policyHolder && item.policyHolder.user) {
            return !!item.policyHolder.fullName ? item.policyHolder.fullName : item.policyHolder.user.fullName;
          } else {
            return '';
          }
        }
      },
      {
        name: L('Owner'),
        fieldName: 'owner.user.fullName',
        minWidth: 170,
        maxWidth: 170,
        onRender: (item: any): any => {
          if(item && item.owner && item.owner.user) {
            return !!item.owner.fullName ? item.owner.fullName : item.owner.user.fullName;
          } else {
            return '';
          }
        }
      },
      {
        name: L('Coowner'),
        fieldName: 'coOwner.user.fullName',
        minWidth: 170,
        maxWidth: 170,
        onRender: (item: any): any => {
          if(item && item.coOwner && item.coOwner.user) {
            return !!item.coOwner.fullName ? item.coOwner.fullName : item.coOwner.user.fullName;
          } else {
            return '';
          }
        }
      },
      {
        name: L('Vehicle user'),
        fieldName: 'vehicleUser.user.fullName',
        minWidth: 170,
        maxWidth: 170,
        onRender: (item: any): any => {
          if(item && item.vehicleUser && item.vehicleUser.user) {
            return !!item.vehicleUser.fullName ? item.vehicleUser.fullName : item.vehicleUser.user.fullName;
          } else {
            return '';
          }
        }
      },
      {
        name: L('VIN number'),
        fieldName: 'vin',
        minWidth: 140,
        maxWidth: 140,
      },
      {
        name: L('Registration number'),
        fieldName: 'registrationNumber',
        minWidth: 140,
        maxWidth: 140,
      },
      {
        name: L('Production year'),
        fieldName: 'year',
        minWidth: 120,
        maxWidth: 120,
      },
      {
        name: L('Mileage'),
        fieldName: 'mileage',
        minWidth: 70,
        maxWidth: 70,
      },
      {
        name: L('Eurotax ID'),
        fieldName: 'eurotaxCarId',
        minWidth: 120,
        maxWidth: 120,
      },
      {
        name: L('Infoexpert ID'),
        fieldName: 'infoExpertId',
        minWidth: 120,
        maxWidth: 120,
      },
      {
        name: L('Vehicle type'),
        fieldName: 'type',
        minWidth: 120,
        maxWidth: 120,
        onRender: (item: any): any => {
          return L(`${item.type}`);
        }
      },
      {
        name: L('Fuel type'),
        fieldName: 'fuelType',
        minWidth: 120,
        maxWidth: 120,
        onRender: (item: any): any => {
          return L(`${item.fuelType}`);
        }
      },
      {
        name: L('Vehicle info'),
        fieldName: 'vehicleInfo',
        minWidth: 120,
        maxWidth: 120,
      },
    ];
  }

  getTitle(): string {
    return L('Vehicles');
  }

  getCrudPermission(): ICrudPermissons {
    return {
      create: true,
      update: true,
      delete: true,
      customActions: false,
    };
  }

  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <>
      <VehiclePanel
        {...props}
      />
    </>
  }
}