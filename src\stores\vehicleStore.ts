import { VehicleConfigDto } from '../services/vehicleConfig/vehicleConfigDto';
import VehicleConfigService from '../services/vehicleConfig/vehicleConfigService';
import { defaultClient } from './clientStore';
import { CrudStoreBase } from './crudStoreBase';

class VehicleStore extends CrudStoreBase<VehicleConfigDto>{
	constructor() {
		super(VehicleConfigService, defaultVehicle)
	}

	public async getByClientId(clientId: string) {
		await VehicleConfigService.getByClientId(clientId).then((result: any) => {
			this.dataSet = {
				totalCount: result.items.length, 
				items: result.items
			};
		}).catch((error: any) => {
			console.error(error);
		});
	}
}

export const defaultVehicle = {
	id: '',
	vin: '',
	firstRegistrationDate: '',
	registrationNumber: '',
	mileage: 0,
	type: '',
	brand: '',
	year: '',
	fuelType: '',
	engineCapacity: '',
	vehicleModel: '',
	enginePower: '',
	eurotaxCarId: '',
	eurotaxCarName: '',
	infoExpertId: '',
	infoExpertName: '',
	vehicleInfo: '',
	lastModificationTime: '',
	lastModifierUserId: 0,
	creationTime: '',
	creatorUserId: 0,
	dmc: 0,
	netWeight: null,
	capacity: 0,
	ownerId: 0,
	owner: defaultClient,
	coOwnerId: 0,
	coOwner: defaultClient,
	vehicleUserId: 0,
	vehicleUser: defaultClient,
	policyHolderId: 0,
	policyHolder: defaultClient,
}

export default VehicleStore;