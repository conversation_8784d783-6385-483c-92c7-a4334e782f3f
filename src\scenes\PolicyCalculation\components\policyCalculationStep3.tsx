import { Callout, Default<PERSON><PERSON>on, <PERSON>alog, DialogFooter, DialogType, FontWeights, IButtonStyles, Icon, IconButton, IIconProps, IStackStyles, IStackTokens, mergeStyleSets, MessageBarType, Modal, PrimaryButton, Stack, Text } from "@fluentui/react";
import React from "react";
import { L } from "../../../lib/abpUtility";
import AppConsts from "../../../lib/appconst";
import policyCalculationAttachedFilesService from "../../../services/attachedFiles/policyCalculationAttachedFilesService";
import blobStorageService from "../../../services/blobStorage/blobStorageService";
import insurancePolicyService from "../../../services/insurancePolicy/insurancePolicyService";
import policyCalculationService from "../../../services/policyCalculation/policyCalculationService";
import testSetService from "../../../services/testSet/testSetService";
import { myTheme } from "../../../styles/theme";
import { isConfigForAG, isConfigForProduction, isConfigForUAT } from "../../../utils/authUtils";
import { jsonViewer } from "../../../utils/jsonViewerUtils";
import { getNumberWithSpaces, isJsonString } from "../../../utils/utils";
import { CheckBoxBase } from "../../BaseComponents/CheckBoxBase";
import { AdjustValuesCustomInputsBox } from "./adjustValuesCustomInputsBox";
import AppConfig from "../../../lib/appconfig";
import { defaultTestSet } from "../../../stores/testSetStore";
import { InsurerAttachedFilePolicyType } from "../../../services/attachedFiles/enums/insurerAttachedFilePolicyTypeEnums";
import vehicleConfigService from "../../../services/vehicleConfig/vehicleConfigService";
import { VehicleConfigDto } from "../../../services/vehicleConfig/vehicleConfigDto";
import { defaultVehicle } from "../../../stores/vehicleStore";
import { CalculationSectionType } from "../../../services/dto/calculationSectionTypeEnums";

const cancelIcon: IIconProps = { iconName: 'Cancel' };

const iconButtonStyles: Partial<IButtonStyles> = {
    root: {
        color: myTheme.palette.neutralPrimary,
        marginLeft: 'auto',
        marginTop: '4px',
        marginRight: '2px',
    },
    rootHovered: {
        color: myTheme.palette.neutralDark,
    },
};

const contentStyles = mergeStyleSets({
    container: {
        display: 'flex',
        flexFlow: 'column nowrap',
        alignItems: 'stretch',
    },
    header: [
        myTheme.fonts.large,
        {
            flex: '1 1 auto',
            color: myTheme.palette.neutralPrimary,
            display: 'flex',
            alignItems: 'center',
            fontWeight: FontWeights.semibold,
            padding: '12px 12px 14px 24px',
        },
    ],
    body: {
        flex: '4 4 auto',
        padding: '0 24px 24px 24px',
        overflowY: 'hidden',
        borderRight: `1px solid ${myTheme.palette.blackTranslucent40}`,
        maxWidth: '33%',
        selectors: {
            ':first-child': {
                padding: '0 60px 24px 24px',
            },
            ':last-child': {
                padding: '0 24px 24px 50px',
            },
            'p': { 
                margin: '14px 0' 
            },
            'p:first-child': { 
                marginTop: 0 
            },
            'p:last-child': { 
                marginBottom: 0 
            },
            '.json': {
                fontSize: '16px',
            },
            '.json > .json__item': {
                display: 'block',
            },
            '.json__item': {
                display: 'none',
                marginTop: '10px',
                paddingLeft: '20px',
                userSelect: 'none',
            },
            '.json__item--collapsible': {
                cursor: 'pointer',
                overflow: 'hidden',
                position: 'relative',
                selectors: {
                    '::before': {
                        content: "'+'",
                        position: 'absolute',
                        left: '5px',
                    },
                    '::after': {
                        backgroundColor: 'lightgrey',
                        content: "''",
                        height: '100%',
                        left: '9px',
                        position: 'absolute',
                        top: '26px',
                        width: '1px',
                    }
                }
            },
            '.json__item--collapsible:hover > .json__key, .json__item--collapsible:hover > .json__value': {
                textDecoration: 'underline',
            },
            '.json__toggle': {
                display: 'none',
            },
            '.json__toggle:checked ~ .json__item': {
                display: 'block',
            },
            '.json__key': {
                color: 'darkblue',
                display: 'inline',
            },
            '.json__key::after': {
                content: "': '",
            },
            '.json__value': {
                display: 'inline',
            },
            '.json__value--string': {
                color: 'green',
            },
            '.json__value--number': {
                color: 'blue',
            },
            '.json__value--boolean': {
                color: 'red',
            },
        },
    },
});

const stackStyles: IStackStyles = {
    root: {
        display: 'flex',
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
    },
};

const customSpacingStackTokens: IStackTokens = {
    childrenGap: '0',
    padding: '25px 0',
};

const classNames = mergeStyleSets({
    calculationBoxWrapper: {
        display: 'flex',
        flexDirection: 'column',
    },
    calculationBox: {
        minWidth: '230px',
        boxSizing: 'border-box',
        display: 'flex', 
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '15px',
        cursor: 'pointer',
        borderRadius: '12px',
        marginRight: '50px',
        marginBottom: '25px',
        marginTop: '15px',
        position: 'relative',
        selectors: {
            '& p': {
                color: myTheme.palette.themePrimary,
                fontWeight: 'bold',
            },
            '& .logo-or-title-wrapper': {
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                maxWidth: '125px',
                maxHeight: '85px',
                minHeight: '65px',
                userSelect: 'none',
                marginTop: '10px',
                selectors: {
                    '& img': {
                        maxWidth: '125px',
                        maxHeight: '85px'
                    },
                }
            },
            '& h3': {
                color: myTheme.palette.blue,
                selectors: {
                    '& span': {
                        color: myTheme.palette.black,
                        fontSize: '0.8rem',
                    }
                }
            }
        }
    },
    calculationBoxError: {
        border: `2px solid ${myTheme.palette.red} !important`,
        cursor: 'not-allowed',
        selectors: {
            '& p': {
                color: myTheme.palette.red,
            },
            '& h3': {
                color: `${myTheme.palette.red} !important`,
                textDecoration: 'line-through',
            }
        }
    },
    calculationBoxWarning: {
        border: `2px solid ${myTheme.palette.yellow} !important`,
        cursor: 'not-allowed',
        selectors: {
            '& p': {
                color: myTheme.palette.yellowDark,
            },
            '& h3': {
                color: `${myTheme.palette.yellowDark} !important`,
                textDecoration: 'line-through',
            }
        }
    },
    calculationBoxYellow: {
        border: `2px solid ${myTheme.palette.yellow} !important`,
        selectors: {
            '& p': {
                color: myTheme.palette.yellowDark,
            },
            '& h3': {
                color: `${myTheme.palette.yellowDark} !important`,
                textDecoration: 'line-through',
            }
        }
    },
    calculationBoxDisabled: {
        border: `2px solid ${myTheme.palette.themeDark} !important`,
        background: `${myTheme.palette.blackTranslucent40} !important`,
        cursor: 'not-allowed',
        selectors: {
            '& p': {
                color: myTheme.palette.themeDark,
            },
            '& h3': {
                color: `${myTheme.palette.themeDark} !important`,
            }
        }
    },
    calculationBoxIcon: {
        color: myTheme.palette.themePrimary,
        fontSize: '22px',
        cursor: 'pointer',
        padding: '0 10px',
    },
    calculationBoxIconError: {
        color: myTheme.palette.red,
    },
    calculationBoxRecommendIcon: {
        color: myTheme.palette.yellow,
        left: 0,
        right: 0,
        marginLeft: 'auto',
        marginRight: 'auto', 
        width: '25px',
    },
    calculationBoxRecommendIconDisabled: {
        color: myTheme.palette.themeDarker,
        pointerEvents: 'none',
    },
    calculationBoxErrorIcon: {
        color: myTheme.palette.red,
        left: '5px',
        right: 'none',
    },
    calculationBoxErrorSecondIcon: {
        color: myTheme.palette.red,
        left: '33px',
        right: 'none',
    },
    calculationBoxWarningIcon: {
        color: `${myTheme.palette.yellow} !important`,
    },
    calculationBoxOutputDetailsIcon: {
        bottom: '5px',
        top: 'none',
        left: '5px',
        right: 'none',
        color: myTheme.palette.neutralQuaternary,
    },
    calculationBoxOutputPDFIcon: {
        bottom: '5px',
        top: 'none',
        right: '5px',
        left: 'none',
        color: myTheme.palette.blue,
    },
    calculationBoxDownloadOutputPDFIcon: {
        right: '35px',
    },
    calculationBoxIconBlocked: {
        color: myTheme.palette.neutralQuaternary,
        pointerEvents: 'none',
    },
    notSelectedCalculationBox: {
        boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.25)',
        padding: '15px',
    },
    selectedCalculationBox: {
        boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.25)',
        background: myTheme.palette.themeLighter,
        selectors: {
            '& p': {
                color: myTheme.palette.blue,
            },
            '& h3': {
                color: `${myTheme.palette.blue} !important`,
            }
        }
    },
    callout: {
        width: 320,
        maxWidth: '90%',
        padding: '20px',
        cursor: 'pointer',
    },
    calloutText: {
        whiteSpace: 'pre-line',
    },
    hide: {
        display: 'none !important',
    },
    messageBar: {
        width: 'fit-content'
    },
    customActionButton: {
        width: 'fit-content',
        padding: '6px 25px',
        marginTop: '10px !important',
        marginRight: '20px',
    },
    loadSpinner: {
        display: 'inline-flex',
        marginTop: 10,

        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    offerCheckBox: {
        marginTop: '-20px',
        height: '20px',
    },
    iconsContainer: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '100%',
        marginTop: '15px'
    }
});

export interface IPolicyCalculationStep3Props {
    calculations: any;
    selectedCalculation: any;
    templateInputsForCalculationAdjust: any;
    inputsTypeValuePairs: any;
    customInputsData: any;
    asyncActionInProgress: boolean | undefined;
    messageBoxData: any;
    gnLanguage: any;
    inputsIdUserFieldsPairs: any;
    getCalculationsRawResponse: any;
    product: any;
    productAttributes: any;
    adjustInputsChangedManually: any;
    lastGetCalculationsPayload: any;
    lastGetCalculationPayload: any;
    selectedClient: string;
    preSettedApkData: any[];
    disabledInputsData: any;
    productType: string;
    eurotaxInfoexpertFormData: any;
    selectedClientContext: any;
    triggerSetApkData: () => void;
    getMapNameByProduct: (product: any) => string;
    mapKeyToId: (mapType: string, key: string) => string;
    toggleAsyncActionInProgress: (bool: boolean) => void;
    setInputsUserFields: (id: string, userFields: any) => void;
    setFilteredCalculations: (calculations: any) => void;
    onCalculationSelect: (calculation: any) => void;
    onInputChange: (id: string, value: any, userFields: any) => void;
    getSingleCalculation: (insurerName: string) => void;
    catchError: (error: any, callType: string, hide?: boolean, type?: MessageBarType) => void;
}

export class PolicyCalculationStep3 extends React.Component<IPolicyCalculationStep3Props> {
    private calloutStatus: any = {};
    private customInputsForCalculationIndex: number = -1;
    private showCustomInputsBox: boolean = false;
    private isModalOpen: boolean = false;
    private insurerJsonRequestValue: string = "";
    private insurerJsonInputValue: string = "";
    private insurerJsonOutputValue: string = "";
    private disableSavePayloadAsTestButton: boolean = false;
    private disableSaveVehicleButton: boolean = false;
    private disableSendApkAndOffersToClientButton: boolean = false;
    private disableDownloadCalculationDetailsFileButton: boolean = false;
    private showVehicleDialog: boolean = false;
    private multiSelectedOfferIndexes: any = {};
    private markAsRecommendedOffer: string = '';
    private linksForGeneratedPDFs: any = {};
    // private filterCalculations(calculation: any) {
    //     return calculation.errors === null;
    // }
    private variantsTableDataShowDialog: boolean = false;
    private currentVariantsTableIconInputId: string | undefined = undefined;

    private async downloadExcelFile() {
        if(this.props.getCalculationsRawResponse) {
            this.props.toggleAsyncActionInProgress(true);

            await policyCalculationService.ExportAsExcel(this.props.getCalculationsRawResponse).then(async (response: any) => {
                if(response.data && response.data.success) {
                    await blobStorageService.getLinkToFile(response.data.result).then((responseFile: any) => {
                        if(responseFile && responseFile.length > 0) {
                            let link: any = document.createElement("a");
                            link.download = response.result;
                            link.href = responseFile;
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                            link = null;
                        } else {
                            console.error(L('Link fo tile is not valid.'));
                            this.props.catchError(L('Link fo tile is not valid.'), "other");
                        }
                    }).catch((error: any) => {
                        console.error(error);
                        this.props.catchError(error, "other");
                    });

                    this.props.toggleAsyncActionInProgress(false);
                } else {
                    console.error(response.error);
                    this.props.catchError(response.error, "other");
                }
            }).catch((error: any) => {
                console.error(error);
                this.props.catchError(error, "other");
            });

            this.props.toggleAsyncActionInProgress(false);
        }
    }

    private async saveCalculateAllPayloadAsTest() {
        if(this.props && this.props.lastGetCalculationsPayload) {
            this.props.toggleAsyncActionInProgress(true);
            
            const calculationId: number = this.props.calculations && this.props.calculations.data && this.props.calculations.data.result &&
                            this.props.calculations.data.result.calculationId ? this.props.calculations.data.result.calculationId : 0;

            await testSetService.create({
                ...defaultTestSet,
                name: `${calculationId} (${L('Saved from calculation')})`,
                payload: JSON.stringify(this.props.lastGetCalculationsPayload),
            }).then(async (response: any) => {
                if(response && response.id && response.id > 0) {
                    this.disableSavePayloadAsTestButton = true;
                    await testSetService.sendTestCalculation(response.id);
                } else if(response && response.error) {
                    console.error(response.error);
                    this.props.catchError(response.error, "other");
                }
            }).catch((error: any) => {
                console.error(error);
                this.props.catchError(error, "other");
            });

            this.props.toggleAsyncActionInProgress(false);
        }
    }

    private async saveVehicleDetails(updateVehicle: boolean) {
        if(this.props && this.props.selectedClient) {
            this.props.toggleAsyncActionInProgress(true);
            
            const saveVehicleRequestBody: VehicleConfigDto = {
                ...defaultVehicle,
                ...this.props.eurotaxInfoexpertFormData,
                policyHolderId: parseInt(this.props.selectedClient), 
                ownerId: this.props.selectedClientContext[CalculationSectionType.Owner].id,
                coOwnerId: this.props.selectedClientContext[CalculationSectionType.CoOwner].id,
                vehicleUserId: this.props.selectedClientContext[CalculationSectionType.User].id,
            };

            if(updateVehicle === true) {
                this.showVehicleDialog = false; 

                let vehicleByVin: any = await vehicleConfigService.getByVin(saveVehicleRequestBody.vin);
                if(vehicleByVin.items && vehicleByVin.totalCount > 0) {
                    saveVehicleRequestBody['id'] = vehicleByVin.items[0].id;

                    await vehicleConfigService.update(saveVehicleRequestBody).then(async (response: any) => {
                        if(response && response.id && response.id > 0) {
                            this.disableSaveVehicleButton = true;
                        } else if(response && response.error) {
                            console.error(response.error);
                            this.props.catchError(response.error, "other");
                        }
                    }).catch((error: any) => {
                        console.error(error);
                        this.props.catchError(error, "other");
                    });
                }
            } else {
                await vehicleConfigService.create(saveVehicleRequestBody).then(async (response: any) => {
                    if(response && response.vehicle && response.vehicle.id && response.vehicle.id > 0) {
                        this.disableSaveVehicleButton = true;
                    } else if(response && (response.isError || (response.errorText && response.errorText.length > 0))) {
                        if(response.errorText === "Pojazd z tym numerem VIN już istnieje w bazie. Czy chcesz zmodyfikować istniejący pojazd?") {
                            this.showVehicleDialog = true; 
                            this.forceUpdate();
                        } else {
                            console.error(response.error);
                            this.props.catchError(response.error, "other");
                        }
                    }
                }).catch((error: any) => {
                    console.error(error);
                    this.props.catchError(error, "other");
                });
            }

            this.props.toggleAsyncActionInProgress(false);
        }
    }

    private toggleJsonOutputModal(index: number, calculation: any, rawRequest?: any) {
        this.insurerJsonRequestValue = !!rawRequest ? rawRequest : 
                                        L('Errors occured while calculating, JSON request is not available.');
        this.insurerJsonOutputValue = !!calculation.jsonOutput ? 
                                        calculation.jsonOutput : 
                                        L('Errors occured while calculating, JSON output is not available.');
        this.insurerJsonInputValue = !!calculation.jsonInput ? 
                                        calculation.jsonInput : 
                                        L('Errors occured while calculating, JSON input is not available.');
        this.isModalOpen = true;
        this.forceUpdate();
    }

    private async generateAndDownloadOfferPDF(index: number, calculation: any, rawRequest?: any) {
        this.props.toggleAsyncActionInProgress(true);

        if(calculation && calculation.calculationId && calculation.insurerName) {
            if(!['TUZ', 'UNIQA', 'Allianz', 'Compensa', 'Proama', 'Generali'].includes(calculation.insurerName)) {
                await insurancePolicyService.DownloadCalculationOfferPDF(calculation.calculationId, calculation.insurerName).then((response: any) => {
                    if(response.success && response.result && response.result.calculationPdf) {
                        let link: any = document.createElement("a");
                        link.download = response.result.calculationPdf.fileName;
                        link.href = response.result.calculationPdf.url;
                        this.linksForGeneratedPDFs[calculation.calculationId] = {download: response.result.calculationPdf.fileName, url: response.result.calculationPdf.url};
    
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        link = null;
                    }
                }).catch((error: any) => {
                    console.error(error);
                    this.props.catchError(error, "other");
                });
            } else {
                alert(L("The insurer does not offer a PDF for this calculation."));
            }
        } else {
            alert(L("The insurer does not offer a PDF for this calculation."));
            console.error(calculation);
        }

        this.props.toggleAsyncActionInProgress(false);
    }

    private toggleCallout(index: number, newState: boolean) {
        if(typeof index !== 'undefined' && index >= 0) {
            this.calloutStatus[index] = newState;
            this.forceUpdate();
        }
    }

    private async sendCalculationsToClient(loopsCounter?: number) {
        this.props.toggleAsyncActionInProgress(true);

        const calculationId: number = this.props.calculations && this.props.calculations.data && this.props.calculations.data.result &&
                            this.props.calculations.data.result.calculationId ? this.props.calculations.data.result.calculationId : 0;

        let payload: any = {
            calculationId: calculationId,
            selectedCalculations: [] as any[],
            currentLanguage: {
                id: this.props.gnLanguage.Id,
                name: this.props.gnLanguage.Name,
                seoCode: this.props.gnLanguage.UniqueSeoCode,
                culture: this.props.gnLanguage.LanguageCulture,
            } as any,
            apkData: [] as any[],
            apkType: this.props.productType as string,
        };

        if(!this.props.preSettedApkData || this.props.preSettedApkData.length === 0) {
            this.props.triggerSetApkData();

            if(!loopsCounter || loopsCounter < 5) {
                setTimeout(() => {
                    this.sendCalculationsToClient(!loopsCounter ? 1 : loopsCounter + 1);
                }, 300);
            } else {
                this.props.catchError('Error occured during attempt of collecting data.', "other");
            }

            return;
        } else {
            payload.apkData = [...this.props.preSettedApkData];
        }

        for(let key in this.multiSelectedOfferIndexes) {
            if(this.multiSelectedOfferIndexes.hasOwnProperty(key)) {
                if(this.multiSelectedOfferIndexes[key].isSelected === true) {
                    const objToPush: any = {
                        calculationId: key,
                        isRecommended: key === this.markAsRecommendedOffer,
                        insurerName: this.multiSelectedOfferIndexes[key].insurerName,
                    };

                    const filteredPayload: any = payload.selectedCalculations.filter((x: any) => x.insurerName === this.multiSelectedOfferIndexes[key].insurerName);

                    if(filteredPayload.length > 0) {
                        const cloneArray: any[] = [];
                        payload.selectedCalculations.forEach((element: any, index: number) => {
                            let duplicateFound: boolean = false;
                            filteredPayload.some((filteredElement: any) => {
                                if(element.calculationId === filteredElement.calculationId) {
                                    duplicateFound = true;
                                    return true;
                                }
                                return false;
                            });

                            if(duplicateFound === false) {
                                cloneArray.push(element);
                            }
                        });
                        cloneArray.push(objToPush);
                        payload.selectedCalculations = cloneArray;
                    } else {
                        payload.selectedCalculations.push(objToPush);
                    }
                }
            }
        }

        await policyCalculationAttachedFilesService.sendApkAndCalculations(payload).then((response: any) => {
            if(response && response.success) {
                this.disableSendApkAndOffersToClientButton = true;
            } else if(response && response.error) {
                console.error(response.error);
                this.props.catchError(response.error, "other");
            }
        }).catch((error: any) => {
            console.error(error);
            this.props.catchError(error, "other");
        });

        this.props.toggleAsyncActionInProgress(false);
    }

    private async downloadCalculationDetailsFile() {
        this.props.toggleAsyncActionInProgress(true);

        const calculationId: number = this.props.calculations && this.props.calculations.data && this.props.calculations.data.result &&
                            this.props.calculations.data.result.calculationId ? this.props.calculations.data.result.calculationId : 0;

        let payload: any = {
            calculationId: calculationId,
            selectedCalculations: [] as any[],
            calculationType: InsurerAttachedFilePolicyType.Vehicle,
        };

        for(let key in this.multiSelectedOfferIndexes) {
            if(this.multiSelectedOfferIndexes.hasOwnProperty(key)) {
                if(this.multiSelectedOfferIndexes[key].isSelected === true) {
                    const objToPush: any = {
                        calculationId: key,
                        isRecommended: key === this.markAsRecommendedOffer,
                        insurerName: this.multiSelectedOfferIndexes[key].insurerName,
                    };

                    const filteredPayload: any = payload.selectedCalculations.filter((x: any) => x.insurerName === this.multiSelectedOfferIndexes[key].insurerName);

                    if(filteredPayload.length > 0) {
                        const cloneArray: any[] = [];
                        payload.selectedCalculations.forEach((element: any, index: number) => {
                            let duplicateFound: boolean = false;
                            filteredPayload.some((filteredElement: any) => {
                                if(element.calculationId === filteredElement.calculationId) {
                                    duplicateFound = true;
                                    return true;
                                }
                                return false;
                            });

                            if(duplicateFound === false) {
                                cloneArray.push(element);
                            }
                        });
                        cloneArray.push(objToPush);
                        payload.selectedCalculations = cloneArray;
                    } else {
                        payload.selectedCalculations.push(objToPush);
                    }
                }
            }
        }

        await policyCalculationAttachedFilesService.getCalculationDetailsFile(payload).then((response: any) => {
            if(response && response.url) {
                let link: any = document.createElement("a");
                link.download = response.displayedFileName;
                link.href = response.url;
                
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                link = null;

                this.disableDownloadCalculationDetailsFileButton = true;
            } else if(response && response.error) {
                console.error(response.error);
                this.props.catchError(response.error, "other");
            }
        }).catch((error: any) => {
            console.error(error);
            this.props.catchError(error, "other");
        });

        this.props.toggleAsyncActionInProgress(false);
    }

    private toggleCustomInputsBox(index: number, calculation: any) {
        if(!this.props.selectedCalculation || (calculation.errors && calculation.errors.length > 0)) {
            if(!calculation.errors || (calculation.errors && calculation.errors.length === 0)) {
                this.props.onCalculationSelect(calculation);
            }

            if(!this.showCustomInputsBox) {
                this.showCustomInputsBox = true;
            }
        } else if(this.props.selectedCalculation) {
            if(this.props.selectedCalculation.gnInsurerId === calculation.gnInsurerId) {
                this.showCustomInputsBox = !this.showCustomInputsBox;
            } else if(!calculation.errors || (calculation.errors && calculation.errors.length === 0)) {
                this.props.onCalculationSelect(calculation);
            }
        }
        
        this.customInputsForCalculationIndex = index;

        if(!this.showCustomInputsBox) {
            this.customInputsForCalculationIndex = -1;
        }

        this.forceUpdate();
    }

    private toggleVariantsTableDialog(initiatorInputId?: string, value?: any) {
        this.variantsTableDataShowDialog = typeof value === "boolean" ? value : true;
        this.currentVariantsTableIconInputId = !!initiatorInputId ? initiatorInputId : undefined;
        this.forceUpdate();
    };

    private openNnwVariantsPDF(initiatorInputId?: string, value?: any) {
        if(this.props.product && this.props.product.SeName === 'nnw-szkolne') {
            if(!!initiatorInputId && this.props.templateInputsForCalculationAdjust && this.props.templateInputsForCalculationAdjust[initiatorInputId]) {
                if(this.props.templateInputsForCalculationAdjust[initiatorInputId].userFields && this.props.templateInputsForCalculationAdjust[initiatorInputId].userFields.length > 0) {
                    this.props.templateInputsForCalculationAdjust[initiatorInputId].userFields.some((userField: any) => {
                        if(userField.Key === "allowTuningForInsurer" && !!userField.Value && (userField.Value.toLowerCase() === 'generali' || userField.Value.toLowerCase() === 'uniqa')) {
                            window.open(AppConfig[`${userField.Value.toLowerCase()}NnwStep3InsuranceOptionsPdfUrl`]);
                            return true;
                        }
                        return false;
                    });
                } else {
                    alert(L('There are no UserFields assigned to the input.'));
                }
            } else {
                alert(L('The input assigned to the action was not recognized.'));
            }
        } else {
            alert(L('Attempting to use a feature in an unexpected product.'));
        }
    };

    render() {
        let rawRequest: any = null;
        let tempCalculations: any = this.props.calculations;
        tempCalculations = tempCalculations && tempCalculations.data ? tempCalculations.data : L('No data');
        if(typeof tempCalculations !== 'string' && tempCalculations.error === null) {
            if(tempCalculations.result && tempCalculations.result.policyCalculations) {
                if(tempCalculations.result.request && rawRequest === null) {
                    rawRequest = tempCalculations.result.request;
                }
                tempCalculations = tempCalculations.result.policyCalculations;
                // tempCalculations = tempCalculations.filter(this.filterCalculations);
                this.props.setFilteredCalculations(tempCalculations);
            } else {
                tempCalculations = L('No data');
            }
        } else {
            tempCalculations = tempCalculations.error;
        }

        const currentCalculationIds: string[] = [];
        if(tempCalculations && Array.isArray(tempCalculations)) {
            tempCalculations.forEach((element: any, index: number) => {
                if(element && !!element.calculationId) {
                    currentCalculationIds.push(element.calculationId);
                } else if(element) {
                    currentCalculationIds.push(`${element.calculationId}${index}`);
                }
            });
        }

        const multiSelectedOfferKeys: string[] = [];
        let multiSelectedOfferIndexesCount: number = 0;
        let markAsRecommendedOfferInSelectedOffers: boolean = false;
        for(let key in this.multiSelectedOfferIndexes) {
            if(this.multiSelectedOfferIndexes.hasOwnProperty(key) && this.multiSelectedOfferIndexes[key].isSelected === true && currentCalculationIds.includes(key)) {
                multiSelectedOfferIndexesCount++;
                multiSelectedOfferKeys.push(key);

                if(!!this.markAsRecommendedOffer && this.markAsRecommendedOffer === key) {
                    markAsRecommendedOfferInSelectedOffers = true;
                }
            }
        };

        return <>
            { !isConfigForProduction() && 
                <Modal
                    titleAriaId={'insurerJsonOutputModal'}
                    isOpen={this.isModalOpen}
                    onDismiss={() => { this.isModalOpen = false; this.forceUpdate(); }}
                    isBlocking={true}
                    containerClassName={contentStyles.container}
                >
                    <div className={contentStyles.header}>
                        <span id={'insurerJsonOutputModal'}>{L("Raw request (left) / prepared JSON input (middle) / insurer output (right):")}</span>
                        <IconButton
                            styles={iconButtonStyles}
                            iconProps={cancelIcon}
                            ariaLabel={L("Close popup modal")}
                            onClick={() => { this.isModalOpen = false; this.forceUpdate(); }}
                        />
                    </div>

                    <Stack horizontal={true}>
                        { typeof this.insurerJsonRequestValue !== 'string' ? 
                            <div className={contentStyles.body} 
                                dangerouslySetInnerHTML={{__html: jsonViewer(this.insurerJsonRequestValue, true)}}>
                            </div> :
                            <div className={contentStyles.body}>
                                { this.insurerJsonRequestValue }
                            </div>
                        }

                        { isJsonString(this.insurerJsonInputValue) ? 
                            <div className={contentStyles.body} 
                                dangerouslySetInnerHTML={{__html: jsonViewer(JSON.parse(this.insurerJsonInputValue), true)}}>
                            </div> :
                            <div className={contentStyles.body}>
                                { this.insurerJsonInputValue }
                            </div>
                        }

                        { isJsonString(this.insurerJsonOutputValue) ? 
                            <div className={contentStyles.body} 
                                dangerouslySetInnerHTML={{__html: jsonViewer(JSON.parse(this.insurerJsonOutputValue), true)}}>
                            </div> :
                            <div className={contentStyles.body}>
                                { this.insurerJsonOutputValue }
                            </div>
                        }
                    </Stack>
                </Modal>
            }

            <AdjustValuesCustomInputsBox templateInputsForCalculationAdjust={this.props.templateInputsForCalculationAdjust} messageBoxData={this.props.messageBoxData}
                asyncActionInProgress={this.props.asyncActionInProgress} selectedCalculation={this.props.selectedCalculation} step3This={this}
                calculations={this.props.calculations} showCustomInputsBox={this.showCustomInputsBox} customInputsForCalculationIndex={this.customInputsForCalculationIndex} 
                getSingleCalculation={ (insurerName: string) => this.props.getSingleCalculation(insurerName) } product={this.props.product}
                onInputChange={ (id: string, value: any, userFields: any) => this.props.onInputChange(id, value, userFields) } productAttributes={this.props.productAttributes}
                inputsTypeValuePairs={this.props.inputsTypeValuePairs} gnLanguage={this.props.gnLanguage} inputsIdUserFieldsPairs={this.props.inputsIdUserFieldsPairs}
                setInputsUserFields={(id: string, userFields: any) => { this.props.setInputsUserFields(id, userFields); }} disabledInputsData={this.props.disabledInputsData}
                adjustInputsChangedManually={this.props.adjustInputsChangedManually} lastGetCalculationPayload={this.props.lastGetCalculationPayload}
                variantsTableDataShowDialog={this.variantsTableDataShowDialog} toggleVariantsTableDataShowDialog={ (value: boolean) => { this.toggleVariantsTableDialog(undefined, value) }}
                currentVariantsTableIconInputId={this.currentVariantsTableIconInputId} 
            />

            <Stack horizontal styles={stackStyles} tokens={customSpacingStackTokens}>
                {Array.isArray(tempCalculations) ?
                    tempCalculations.map((calculation: any, index: number) => {
                        const getCalculationId: string = !!calculation.calculationId ? calculation.calculationId : `${calculation.calculationId}${index}`;

                        return <div className={classNames.calculationBoxWrapper} key={index}>
                            <div key={index} 
                                onClick={() => (calculation.success && (this.customInputsForCalculationIndex < 0 || this.customInputsForCalculationIndex === index)) && this.props.onCalculationSelect((calculation))}
                                className={`${classNames.calculationBox} ${calculation.success ? '' : (calculation.rideryErrors && calculation.rideryErrors.length > 0 ? classNames.calculationBoxWarning : classNames.calculationBoxError)}
                                    ${(calculation.additionalInformations && calculation.additionalInformations.length > 0) ? classNames.notSelectedCalculationBox : ((typeof this.props.selectedCalculation !== 'undefined' && this.props.selectedCalculation.gnInsurerId === calculation.gnInsurerId) ? classNames.selectedCalculationBox : classNames.notSelectedCalculationBox)}
                                    ${(calculation.additionalInformations && calculation.additionalInformations.length > 0) && classNames.calculationBoxYellow} ${this.customInputsForCalculationIndex >= 0 && this.customInputsForCalculationIndex !== index ? classNames.calculationBoxDisabled : ''}
                                `}
                            >
                                <div className="logo-or-title-wrapper">
                                    { calculation.insurerLogo && !!calculation.insurerLogo ?
                                        <img src={calculation.insurerLogo} alt={`${calculation.insurerName} logo`} />
                                        :
                                        <p>{calculation.insurerName}</p> 
                                    }
                                    { calculation.errors && calculation.errors.length > 0 &&
                                        <Icon onClick={() => this.toggleCallout(index, true)} iconName={'WarningSolid'} className={`${classNames.calculationBoxIcon} ${classNames.calculationBoxErrorIcon} ${(calculation.rideryErrors && calculation.rideryErrors.length > 0) && classNames.calculationBoxWarningIcon}`}
                                            id={`calculationOffer${index}`} title={L('Errors have occurred')} />
                                    }

                                    { calculation.additionalInformations && calculation.additionalInformations.length > 0 &&
                                        <Icon onClick={() => this.toggleCallout(index, true)} iconName={'WarningSolid'} className={`${classNames.calculationBoxIcon} ${classNames.calculationBoxErrorIcon} ${classNames.calculationBoxWarningIcon} ${(calculation.errors && calculation.errors.length > 0) || (calculation.rideryErrors && calculation.rideryErrors.length > 0) ? classNames.calculationBoxErrorSecondIcon : ''}`}
                                            id={`${(calculation.errors && calculation.errors.length > 0) ? `calculationSecondOffer${index}` : `calculationOffer${index}` }`} title={L('Errors have occurred')} />
                                    }
                                    {(!calculation.errors || calculation.errors.length === 0) &&
                                        <Icon onClick={ (e) => {
                                                e.stopPropagation();
                                                if (multiSelectedOfferKeys.includes(getCalculationId)) {
                                                    this.markAsRecommendedOffer = this.markAsRecommendedOffer === getCalculationId ? '' : getCalculationId;
                                                    this.forceUpdate();
                                                }
                                            }} 
                                            iconName={this.markAsRecommendedOffer === getCalculationId ? 'FavoriteStarFill' : 'FavoriteStar'}
                                            className={`${classNames.calculationBoxIcon} ${classNames.calculationBoxRecommendIcon} 
                                                            ${!multiSelectedOfferKeys.includes(getCalculationId) ? classNames.calculationBoxRecommendIconDisabled : ''}`}
                                            id={`calculationOfferRecommend${index}`} title={L('Mark as recommended')} />
                                    }
                                </div>

                                <h3>{calculation.price} <span>{calculation.currency}</span></h3>

                                { (calculation.insuranceSum) ?
                                    <h5 style={{marginTop: 0, marginBottom: 10}}>SU: {getNumberWithSpaces(calculation.insuranceSum)} <span>{calculation.currency}</span></h5>
                                    :
                                    <span style={{display: 'block', height: 22}}></span>
                                }

                                <div className={classNames.iconsContainer}>
                                    <Icon onClick={ (e) => { e.stopPropagation(); this.toggleCustomInputsBox(index, calculation); }} 
                                        iconName={'Settings'} style={Object.keys(this.props.templateInputsForCalculationAdjust).length > 0 ? {} : {color: '#d30000'}} 
                                        className={`${classNames.calculationBoxIcon} ${(this.props.asyncActionInProgress || Object.keys(this.props.templateInputsForCalculationAdjust).length === 0) && classNames.calculationBoxIconBlocked}`}
                                        id={`calculationOfferAdjust${index}`} title={L('Adjust values and recalculate')} />

                                    { !isConfigForProduction() && 
                                        <Icon onClick={ (e) => { e.stopPropagation(); this.toggleJsonOutputModal(index, calculation, rawRequest); }} iconName={'Search'} 
                                            className={`${classNames.calculationBoxIcon} ${classNames.calculationBoxOutputDetailsIcon}`}
                                            id={`calculationOfferJsonOutput${index}`} title={L('See detailed JSON input / output for this offer')} />
                                    }

                                    {this.linksForGeneratedPDFs[getCalculationId] && 
                                        <a href={this.linksForGeneratedPDFs[getCalculationId].url} download={this.linksForGeneratedPDFs[getCalculationId].download}>
                                            <Icon onClick={ (e) => { e.stopPropagation(); }} iconName={'DownloadDocument'} 
                                                className={`${classNames.calculationBoxIcon} ${classNames.calculationBoxOutputPDFIcon} ${classNames.calculationBoxDownloadOutputPDFIcon} ${this.props.asyncActionInProgress && classNames.calculationBoxIconBlocked}`}
                                                id={`calculationOfferDownloadPDF${index}`} title={L('Download generated PDF file with this offer')} />
                                        </a>
                                    }
                                    
                                    <Icon onClick={ (e) => { e.stopPropagation(); this.generateAndDownloadOfferPDF(index, calculation, rawRequest); }} iconName={'PDF'} 
                                        className={`${classNames.calculationBoxIcon} ${classNames.calculationBoxOutputPDFIcon} ${this.props.asyncActionInProgress && classNames.calculationBoxIconBlocked}`}
                                        id={`calculationOfferGeneratePDF${index}`} 
                                        title={!!calculation.calculationId && !['TUZ', 'UNIQA', 'Allianz', 'Compensa', 'Proama', 'Generali'].includes(calculation.insurerName) ? L('Generate and download PDF file with this offer') : L("The insurer does not offer a PDF for this calculation.")}
                                        style={!!calculation.calculationId && !['TUZ', 'UNIQA', 'Allianz', 'Compensa', 'Proama', 'Generali'].includes(calculation.insurerName) ? {} : {color: '#d30000'}} />
                                </div>

                                {(this.calloutStatus[index] && this.calloutStatus[index] === true) && (
                                    <Callout
                                        className={classNames.callout}
                                        gapSpace={0}
                                        target={`#calculationOffer${index}`}
                                        onDismiss={ () => this.toggleCallout(index, false) }
                                        setInitialFocus
                                    >
                                        <Text className={classNames.calloutText} block variant="small">
                                            { calculation.rideryErrors && calculation.rideryErrors.length > 0 ?
                                                calculation.rideryErrors.map((rideryError: any) => {
                                                    return `${L(rideryError)} \n\r\n\r`
                                                }) :
                                                (calculation.errors && calculation.errors.length > 0 ?
                                                    calculation.errors.map((error: any) => {
                                                        return `${L(error)} \n\r\n\r`
                                                    }) : '')
                                            }
                                            {calculation.additionalInformations && calculation.additionalInformations.length > 0
                                                &&
                                                calculation.additionalInformations.map((additionalInformation: any) => {
                                                    return `${L(additionalInformation)} \n\r\n\r`
                                            })}
                                        </Text>
                                    </Callout>
                                )}
                            </div>

                            {!calculation.errors || calculation.errors.length === 0 ?
                                <CheckBoxBase customClassName={classNames.offerCheckBox} key={`checkBoxForOffer${index}`} label={L('Include for sending to client')} 
                                    value={this.multiSelectedOfferIndexes[getCalculationId] && this.multiSelectedOfferIndexes[getCalculationId].isSelected ? true : false}
                                    disabled={multiSelectedOfferIndexesCount >= 3 &&
                                                ((this.multiSelectedOfferIndexes[getCalculationId] && this.multiSelectedOfferIndexes[getCalculationId].isSelected === false) || 
                                                !this.multiSelectedOfferIndexes[getCalculationId])}
                                    onChange={(e: any) => {
                                        if(typeof e !== 'undefined' && (multiSelectedOfferIndexesCount < 3 || e === false)) {
                                            this.multiSelectedOfferIndexes[getCalculationId] = {
                                                isSelected: e,
                                                calculationId: getCalculationId,
                                                insurerName: calculation.insurerName
                                            };

                                            if(this.markAsRecommendedOffer === getCalculationId) {
                                                this.markAsRecommendedOffer = '';
                                            }
                                        } 
                                        // else {
                                        //     delete this.multiSelectedOfferIndexes[getCalculationId];
                                        // }
                                        this.forceUpdate();
                                    }}
                                />
                                :
                                <div style={{display: 'block', height: '20px'}}></div>
                            }
                        </div>;
                    })
                    :
                    <p>{tempCalculations}</p>
                }
            </Stack>

            {AppConsts.allowedContent === 'ALL' &&
                <PrimaryButton className={classNames.customActionButton} theme={myTheme} text={L('Send to client')} iconProps={{ iconName: this.disableSendApkAndOffersToClientButton ? 'CheckMark' : 'Send' }}
                    type={'button'} disabled={this.props.asyncActionInProgress || multiSelectedOfferIndexesCount <= 0 || multiSelectedOfferIndexesCount > 3 || !this.markAsRecommendedOffer || !markAsRecommendedOfferInSelectedOffers}
                    onClick={() => this.sendCalculationsToClient()}
                />
            }

            {(AppConsts.allowedContent === 'ALL' && this.props.mapKeyToId("mapProductIdToType", this.props.product.Id) === 'Vehicle') && 
                <DefaultButton className={classNames.customActionButton} text={L('Download comparative list')} iconProps={{ iconName: this.disableDownloadCalculationDetailsFileButton ? 'CheckMark' : 'Page' }}
                    type={'button'} onClick={() => this.downloadCalculationDetailsFile()} disabled={this.props.asyncActionInProgress || multiSelectedOfferIndexesCount <= 0 || multiSelectedOfferIndexesCount > 3 || !this.markAsRecommendedOffer || !markAsRecommendedOfferInSelectedOffers} />
            }

            {(AppConsts.allowedContent === 'ALL' && this.props.mapKeyToId("mapProductIdToType", this.props.product.Id) === 'Vehicle') && 
                <DefaultButton className={classNames.customActionButton} text={L('Save vehicle')} iconProps={{ iconName: this.disableSaveVehicleButton ? 'CheckMark' : 'Save' }}
                    type={'button'} onClick={() => this.saveVehicleDetails(false)} disabled={this.props.asyncActionInProgress || this.disableSaveVehicleButton} />
            }

            {(AppConsts.allowedContent === 'ALL') && 
                <DefaultButton className={classNames.customActionButton} theme={myTheme} text={L('Save as test')} iconProps={{ iconName: this.disableSavePayloadAsTestButton ? 'CheckMark' : 'Save' }}
                    type={'button'} onClick={() => this.saveCalculateAllPayloadAsTest()} disabled={this.props.asyncActionInProgress || this.disableSavePayloadAsTestButton} />
            }

            {(!isConfigForAG() && !isConfigForUAT() && !isConfigForProduction()) &&
                <DefaultButton className={classNames.customActionButton} theme={myTheme} text={L('Export to Excel file')} iconProps={{ iconName: 'Page' }}
                    type={'button'} onClick={() => this.downloadExcelFile()} disabled={this.props.asyncActionInProgress} />
            }

            <Dialog
                hidden={!this.showVehicleDialog}
                onDismiss={() => { this.showVehicleDialog = false; this.forceUpdate(); }}
                dialogContentProps={{
                    type: DialogType.normal,
                    title: L("Do you want to update the details of this vehicle?"),
                    closeButtonAriaLabel: L('Close'),
                }}
                modalProps={{
                    // titleAriaId: this._labelId,
                    // subtitleAriaId: this._subTextId,
                    isBlocking: true,
                    styles: { main: { maxWidth: 450 } },
                }}
                theme={myTheme}
            >
                <DialogFooter theme={myTheme}>
                    <DefaultButton theme={myTheme} onClick={() => { this.showVehicleDialog = false; this.forceUpdate(); }} text={L('No')} />
                    <PrimaryButton
                        onClick={async () => this.saveVehicleDetails(true)}
                        text={L('Yes')}
                        theme={myTheme}
                        disabled={false}
                    />
                </DialogFooter>
            </Dialog>
        </>;
    }
}